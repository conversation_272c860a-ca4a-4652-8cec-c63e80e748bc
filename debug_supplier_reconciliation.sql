-- 调试供应商对账查询问题的SQL脚本

-- 1. 检查供应商 '1878734518961074177' 在指定时间范围内是否有任何数据（不考虑对账状态）
SELECT '检查供应商是否有数据（不考虑对账状态）' as debug_step;
SELECT COUNT(*) as total_records
FROM (
    SELECT
        ors.order_id,
        ors.supplier_id AS supplierOrgId,
        ors.confirm_time AS receivingDate,
        ors.ship_enterprise_id AS twoSupplierOrgId,
        dtl.is_reconciliation 
    FROM order_ship ors
    INNER JOIN order_ship_dtl dtl ON ors.bill_Id = dtl.bill_id 
    WHERE ors.type = 2 
        AND ors.order_class != 1 
        AND ors.is_delete = 0 
        AND dtl.is_delete = 0 
    UNION ALL
    SELECT
        ore.order_id,
        ore.supplier_id AS supplierOrgId,
        ore.flish_time AS receivingDate,
        ore.ship_enterprise_id AS twoSupplierOrgId,
        ori.is_reconciliation 
    FROM order_return ore
    INNER JOIN order_return_item ori ON ore.order_return_id = ori.order_return_id 
    WHERE ore.state = 3 
        AND ore.is_out = 1 
        AND ore.is_delete = 0 
        AND ori.is_delete = 0 
) res 
WHERE res.supplierOrgId = '1878734518961074177' 
    AND res.receivingDate > '2021-08-01 00:00:00' 
    AND res.receivingDate < '2025-09-30 00:00:00';

-- 2. 检查该供应商的对账状态分布
SELECT '检查对账状态分布' as debug_step;
SELECT 
    is_reconciliation,
    COUNT(*) as count
FROM (
    SELECT
        ors.order_id,
        ors.supplier_id AS supplierOrgId,
        ors.confirm_time AS receivingDate,
        ors.ship_enterprise_id AS twoSupplierOrgId,
        dtl.is_reconciliation 
    FROM order_ship ors
    INNER JOIN order_ship_dtl dtl ON ors.bill_Id = dtl.bill_id 
    WHERE ors.type = 2 
        AND ors.order_class != 1 
        AND ors.is_delete = 0 
        AND dtl.is_delete = 0 
    UNION ALL
    SELECT
        ore.order_id,
        ore.supplier_id AS supplierOrgId,
        ore.flish_time AS receivingDate,
        ore.ship_enterprise_id AS twoSupplierOrgId,
        ori.is_reconciliation 
    FROM order_return ore
    INNER JOIN order_return_item ori ON ore.order_return_id = ori.order_return_id 
    WHERE ore.state = 3 
        AND ore.is_out = 1 
        AND ore.is_delete = 0 
        AND ori.is_delete = 0 
) res 
WHERE res.supplierOrgId = '1878734518961074177' 
    AND res.receivingDate > '2021-08-01 00:00:00' 
    AND res.receivingDate < '2025-09-30 00:00:00'
GROUP BY is_reconciliation;

-- 3. 检查该供应商的时间范围分布
SELECT '检查时间范围分布' as debug_step;
SELECT 
    DATE(receivingDate) as receive_date,
    COUNT(*) as count,
    SUM(CASE WHEN is_reconciliation = 0 THEN 1 ELSE 0 END) as unreconciled_count
FROM (
    SELECT
        ors.order_id,
        ors.supplier_id AS supplierOrgId,
        ors.confirm_time AS receivingDate,
        ors.ship_enterprise_id AS twoSupplierOrgId,
        dtl.is_reconciliation 
    FROM order_ship ors
    INNER JOIN order_ship_dtl dtl ON ors.bill_Id = dtl.bill_id 
    WHERE ors.type = 2 
        AND ors.order_class != 1 
        AND ors.is_delete = 0 
        AND dtl.is_delete = 0 
    UNION ALL
    SELECT
        ore.order_id,
        ore.supplier_id AS supplierOrgId,
        ore.flish_time AS receivingDate,
        ore.ship_enterprise_id AS twoSupplierOrgId,
        ori.is_reconciliation 
    FROM order_return ore
    INNER JOIN order_return_item ori ON ore.order_return_id = ori.order_return_id 
    WHERE ore.state = 3 
        AND ore.is_out = 1 
        AND ore.is_delete = 0 
        AND ori.is_delete = 0 
) res 
WHERE res.supplierOrgId = '1878734518961074177' 
    AND res.receivingDate > '2021-08-01 00:00:00' 
    AND res.receivingDate < '2025-09-30 00:00:00'
GROUP BY DATE(receivingDate)
ORDER BY receive_date DESC
LIMIT 10;

-- 4. 检查该供应商是否存在于系统中
SELECT '检查供应商是否存在' as debug_step;
SELECT enterprise_id, enterprise_name 
FROM enterprise_info 
WHERE enterprise_id = '1878734518961074177';

-- 5. 对比两个供应商的基本信息
SELECT '对比两个供应商信息' as debug_step;
SELECT enterprise_id, enterprise_name, enterprise_type, is_delete
FROM enterprise_info 
WHERE enterprise_id IN ('1878734518961074177', '1880074382960799747');

-- 6. 检查是否有二级供应商的限制
SELECT '检查二级供应商分布' as debug_step;
SELECT 
    twoSupplierOrgId,
    COUNT(*) as count
FROM (
    SELECT
        ors.order_id,
        ors.supplier_id AS supplierOrgId,
        ors.confirm_time AS receivingDate,
        ors.ship_enterprise_id AS twoSupplierOrgId,
        dtl.is_reconciliation 
    FROM order_ship ors
    INNER JOIN order_ship_dtl dtl ON ors.bill_Id = dtl.bill_id 
    WHERE ors.type = 2 
        AND ors.order_class != 1 
        AND ors.is_delete = 0 
        AND dtl.is_delete = 0 
    UNION ALL
    SELECT
        ore.order_id,
        ore.supplier_id AS supplierOrgId,
        ore.flish_time AS receivingDate,
        ore.ship_enterprise_id AS twoSupplierOrgId,
        ori.is_reconciliation 
    FROM order_return ore
    INNER JOIN order_return_item ori ON ore.order_return_id = ori.order_return_id 
    WHERE ore.state = 3 
        AND ore.is_out = 1 
        AND ore.is_delete = 0 
        AND ori.is_delete = 0 
) res 
WHERE res.supplierOrgId = '1878734518961074177' 
    AND res.receivingDate > '2021-08-01 00:00:00' 
    AND res.receivingDate < '2025-09-30 00:00:00'
GROUP BY twoSupplierOrgId;
