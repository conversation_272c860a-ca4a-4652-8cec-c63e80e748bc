FROM java:8
ENV TZ 'Asia/Shanghai'
#ENV LANG en_US.UTF-8
#ENV LANGUAGE en_US:en
#ENV LC_ALL en_US.UTF-8
VOLUME /tmp4/dockerJar
ADD ./target/*.jar mall-auther-service.jar
ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom -Dfile.encoding=UTF-8","-jar","/mall-auther-service.jar"]
#ENTRYPOINT ["java","-jar","mall-service.jar"]
#FROM：指定存在的镜像，java:8是我刚刚拉取的镜像，运行的基础。
#VOLUME：指向的一个临时文件，用于存储tomcat工作。
#ADD：复制文件并且重命名文件。
#ENTRYPOINT：初始化配置或者自定义配置。