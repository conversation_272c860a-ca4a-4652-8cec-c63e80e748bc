server:
  port: 9012
  tomcat:
    uri-encoding: UTF-8
    max-swallow-size: -1 #内置tomcat限制文件最大大小 -1为不限制
spring:
  #管理文件上传和请求数据大小
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB  #单文件最大限制
      max-request-size: 500MB #总文件最大限制
      file-size-threshold: 500MB
  main:
    allow-bean-definition-overriding: true
  application:
    name: mall-auth
  #本地环境
  profiles:
    active: dev
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

#接口文档
knife4j:
  enable: true
  setting:
    enableSwaggerModels: true
    swaggerModelName: 实体类列表

#mybatisPlus配置
mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      logic-delete-field: isDelete #默认deleted
      logic-delete-value: -1
      logic-not-delete-value: 0
      id-type: auto

mall:
  type: 0 #物资商场为material(0)，设备商场为 device(1)