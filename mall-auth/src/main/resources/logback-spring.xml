<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <springProperty scope="context" name="springAppName" source="spring.application.name"/>
    <!--定义参数常量-->

    <!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
    <property name="log.level" value="INFO"/>
    <!--定义文件输出路径-->
    <property name="log.path" value="logs/material" />

    <!--读取项目启动的环境是哪一个 -->
    <springProperty scope="context" name="active" source="spring.profiles.active" defaultValue="dev"/>

    <!--保存历史日志文件数-->
    <property name="log.maxHistory" value="999"/>
    <!--单个文件最大大小-->
    <property name="log.maxFileSize" value="10MB"/>
    <!--所有日志最多占有多大硬盘-->
    <property name="log.totalSizeCap" value="100GB"/>


    <!--日志颜色-->
    <property name="log.colorPattern" value="%magenta(%d{yyyy-MM-dd HH:mm:ss.SSS}) %highlight(%-5level) %boldCyan([${springAppName:-},%X{X-B3-TraceId:-},%X{X-B3-SpanId:-},%X{X-Span-Export:-}]) %yellow(%thread) %green(%logger) row:%L %msg%n"/>
    <!--日志的输出格式-->
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [${springAppName:-},%X{X-B3-TraceId:-},%X{X-B3-SpanId:-},%X{X-Span-Export:-}] %thread %logger row:%L %msg%n"/>


    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.colorPattern}</pattern>
        </encoder>
    </appender>


    <!--输出到文件-->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">

        <!--滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--归档文件名称-->
            <fileNamePattern>${log.path}/info/info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--保存历史日志文件数-->
            <maxHistory>${log.maxHistory}</maxHistory>
            <!--单个日志最大容量 至少10MB才能看得出来-->
            <maxFileSize>${log.maxFileSize}</maxFileSize>
            <!--所有日志最多占多大容量-->
            <totalSizeCap>${log.totalSizeCap}</totalSizeCap>
            <!--默认不删除多余的文件-->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <!--暂时不区分目录存日志-->
        <!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
        <!--            <level>DEBUG</level>-->
        <!--            <onMatch>ACCEPT</onMatch>-->
        <!--            <onMismatch>DENY</onMismatch>-->
        <!--        </filter>-->
    </appender>

<!--    <appender name="ELASTIC" class="com.internetitem.logback.elasticsearch.ElasticsearchAppender">-->
<!--        <url>http://192.168.91.7:9200/_bulk</url>-->
<!--        <index>mall-material-log-${active}</index>   &lt;!&ndash; 根据启动环境为索引命名,区分各个环境日志 &ndash;&gt;-->
<!--        <type>${active}</type>-->
<!--        <connectTimeout>30000</connectTimeout> &lt;!&ndash; optional (in ms, default 30000) &ndash;&gt;-->
<!--        <errorsToStderr>false</errorsToStderr> &lt;!&ndash; optional (default false) &ndash;&gt;-->
<!--        <includeCallerData>false</includeCallerData> &lt;!&ndash; optional (default false) &ndash;&gt;-->
<!--        <logsToStderr>false</logsToStderr> &lt;!&ndash; optional (default false) &ndash;&gt;-->
<!--        <maxQueueSize>104857600</maxQueueSize> &lt;!&ndash; optional (default 104857600) &ndash;&gt;-->
<!--        <maxRetries>3</maxRetries> &lt;!&ndash; optional (default 3) &ndash;&gt;-->
<!--        <readTimeout>30000</readTimeout> &lt;!&ndash; optional (in ms, default 30000) &ndash;&gt;-->
<!--        <sleepTime>250</sleepTime> &lt;!&ndash; optional (in ms, default 250) &ndash;&gt;-->
<!--        <rawJsonMessage>false</rawJsonMessage> &lt;!&ndash; optional (default false) &ndash;&gt;-->
<!--        <includeMdc>false</includeMdc> &lt;!&ndash; optional (default false) &ndash;&gt;-->
<!--        <maxMessageSize>-1</maxMessageSize> &lt;!&ndash; optional (default -1 &ndash;&gt;-->
<!--        <authentication class="com.internetitem.logback.elasticsearch.config.BasicAuthentication"/> &lt;!&ndash; optional &ndash;&gt;-->
<!--        <properties>-->
<!--            <property>-->
<!--                <name>host</name>-->
<!--                <value>${HOSTNAME}</value>-->
<!--                <allowEmpty>false</allowEmpty>-->
<!--            </property>-->
<!--            <property>-->
<!--                <name>level</name>-->
<!--                <value>%level</value>-->
<!--            </property>-->
<!--            <property>-->
<!--                <name>thread</name>-->
<!--                <value>%thread</value>-->
<!--            </property>-->
<!--            <property>-->
<!--                <name>stacktrace</name>-->
<!--                <value>%ex</value>-->
<!--            </property>-->
<!--            <property>-->
<!--                <name>logger</name>-->
<!--                <value>%logger</value>-->
<!--            </property>-->
<!--            <property>-->
<!--                <name>env</name>-->
<!--                <value>${active}</value>-->
<!--            </property>-->
<!--            <property>-->
<!--                <name>row</name>-->
<!--                <value>%L</value>-->
<!--            </property>-->
<!--            <property>-->
<!--                <name>date</name>-->
<!--                <value>%d{yyyy-MM-dd HH:mm:ss}</value>-->
<!--            </property>-->
<!--        </properties>-->
<!--        <headers>-->
<!--            <header>-->
<!--                <name>Content-Type</name>-->
<!--                <value>application/json</value>-->
<!--            </header>-->
<!--        </headers>-->
<!--    </appender>-->


    <root level="debug">
        <appender-ref ref="console" />
    </root>

    <root level="info">
        <appender-ref ref="file_info" />
    </root>

<!--&lt;!&ndash;    输出到es&ndash;&gt;-->
<!--    <root level="info">-->
<!--        <appender-ref ref="ELASTIC" />-->
<!--    </root>-->

<!--    生产才记录-->
<!--    <springProfile name="prod">-->
<!--        <root level="info">-->
<!--            <appender-ref ref="ELASTIC" />-->
<!--        </root>-->
<!--    </springProfile>-->
</configuration>