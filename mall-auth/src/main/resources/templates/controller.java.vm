package ${package.Controller};
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import ${package.Service}.${table.serviceName};
import ${package.Entity}.${entity};

import java.util.List;

/**
 * @描述：$!{table.comment}控制类
 * @作者: ${author}
 * @日期: ${date}
 */
@RestController
@RequestMapping("/${table.entityPath}")
@Api(tags = "$!{table.comment}")
public class ${table.controllerName}{

@Autowired
public ${table.serviceName} ${table.entityPath}Service;

@PostMapping("/listByEntity")
@ApiOperation(value = "根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<${entity}> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= ${table.entityPath}Service.queryPage(jsonObject,new LambdaQueryWrapper<${entity}>());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<${entity}> findById(String id){
    ${entity} ${table.entityPath} = ${table.entityPath}Service.getById(id);
        return R.success(${table.entityPath});
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
public R save(@RequestBody ${entity} ${table.entityPath}){
    ${table.entityPath}Service.create(${table.entityPath});
        return R.success();
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
public R update(@RequestBody ${entity} ${table.entityPath}){
    ${table.entityPath}Service.update(${table.entityPath});
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    ${table.entityPath}Service.delete(id);
        return R.success();
        }


@PostMapping("/deleteBatch")
@ApiOperation(value = "根据主键批量删除")
public R deleteBatch(@RequestBody List<String> ids){
    ${table.entityPath}Service.removeByIds(ids);
        return R.success();
        }
}

