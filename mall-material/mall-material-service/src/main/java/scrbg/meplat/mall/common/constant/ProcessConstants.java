package scrbg.meplat.mall.common.constant;

public interface ProcessConstants {

    /**
     * 发布竞价
     */
    public static final String PUBLISH_BIDDING_PROCESS_ID = "18";

    /**
     * 竞价管理-竞价结果   选择报价排名第一的供应商
     */
    public static final String BIDDING_PROCESS_ID_FIRST = "7";

    /**
     * 竞价管理-竞价结果   因特殊原因未选择报价排名第一的供应商及存在多个并列第一的供应商
     */
    public static final String BIDDING_PROCESS_ID_NO_FIRST = "8";

}
