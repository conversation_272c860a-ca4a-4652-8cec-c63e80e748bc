package scrbg.meplat.mall.controller.stockManage;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.OutboundSettlementManage;
import scrbg.meplat.mall.service.stockManage.OutboundSettlementService;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/stock/outboundSettlement")
@ApiSort(value = 500)
@Api(tags = "出库结算管理（后台）")
public class OutboundSettlementController {

    private OutboundSettlementService outboundSettlementService;


    @Autowired
    public void setOutboundSettlementService(OutboundSettlementService outboundSettlementService) {
        this.outboundSettlementService = outboundSettlementService;
    }

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<OutboundSettlementManage> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils<OutboundSettlementManage> page = outboundSettlementService.queryPage(jsonObject, new LambdaQueryWrapper<OutboundSettlementManage>());
        return PageR.success(page);
    }

    @GetMapping("/export")
    @ApiOperation(value = "导出出库结算单")
    public R<Object> export(String id, HttpServletResponse response) {
        outboundSettlementService.export(id,response);
        return R.success("出库结算单导出成功");
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增入库结算单")
    public R<Object> save(@RequestBody OutboundSettlementManage outboundSettlementManage) {
        outboundSettlementManage.setOutboundType(1);
        outboundSettlementService.saveSettlement(outboundSettlementManage);
        return R.success();
    }
    @GetMapping("/updateState")
    @ApiOperation(value = "改变出库结算单状态")
    @Transactional
    public R<Object> updateState(String id, int state) {
        outboundSettlementService.updateState(id, state);
        return R.success();
    }
    @PostMapping("/saveAndSubmit")
    @ApiOperation(value = "新增入库结算单")
    public R<Object> saveAndSubmit(@RequestBody OutboundSettlementManage outboundSettlementManage) {
        outboundSettlementManage.setOutboundType(1);
        outboundSettlementService.saveAndSubmitSettlement(outboundSettlementManage);
        return R.success();
    }
}
