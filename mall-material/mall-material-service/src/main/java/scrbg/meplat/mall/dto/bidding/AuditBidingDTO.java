package scrbg.meplat.mall.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AuditBidingDTO {

    /**
     * -1：延长截止时间；1：竞价记录-审核；2：竞价记录-作废
     */
    private Integer pageType;

    @ApiModelProperty(value = "竞价id")
    private String biddingId;

    @ApiModelProperty(value = "竞价记录id")
    private String bidRecordId;

    @ApiModelProperty(value = "经办人")
    private String handledById;

    @ApiModelProperty(value = "审核结果：1：统一；0：返回修改")
    private Integer auditResultType;

    @ApiModelProperty(value = "作废原因：1：数据错误；2：不符合业务要求；3：不在此功能录入；4：其他")
    private Integer cancelResultType;

    /**
     * 竞价记录 --前端使用
     */
    private List<BidOfferInfoVo> bidOfferInfoVos;

    /**
     * 1:第一条；2：非第一条
     */
    private Integer auditProcessType;

    /**
     * 通过公司
     */
    private String  biddingOrderCompany;

    @ApiModelProperty(value = "原因")
    private String auditResult;
}
