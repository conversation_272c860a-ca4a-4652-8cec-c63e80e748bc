package scrbg.meplat.mall.dto.bidding;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.scrbg.common.entity.Amount;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: maill_api
 *
 * @description: 竞价记录明细和物资关联数据对象
 *
 * @author: 代文翰
 *
 * @create: 2023-07-20 22:12
 **/
@Data
public class BidOfferInfoVo {
    @ApiModelProperty(value = "商品名称")

    private String productName;
    @ApiModelProperty(value = "规格型号")

    private String spec;

    @ApiModelProperty(value = "竞价采购商品id")
    private String biddingProductId;

    @ApiModelProperty(value = "商品材质")

    private String productTexture;

    @ApiModelProperty(value = "数量")

    private BigDecimal num;
    @ApiModelProperty(value = "不含税到场单价")

    private BigDecimal bidPrice;
    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;
    @ApiModelProperty(value = "含税到场单价")

    private BigDecimal bidRatePrice;
    @ApiModelProperty(value = "含税总金额")

    private BigDecimal bidRateAmount;
    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal bidAmount;

    @ExcelProperty("供货地址")
    private String deliveryAddress;
    @ExcelProperty("供货时间")
    private String deliveryDate;
    @ExcelProperty(value = "计量单位")
    private String unit;
    @ApiModelProperty(value = "备注")
    private String remarks;
    @ApiModelProperty(value = "参考单价（限制最高价）")

    private BigDecimal referencePrice;
    @ApiModelProperty(value = "网价（浮动价格使用）")

    private BigDecimal netPrice;

    @ApiModelProperty(value = "固定费用（浮动价格使用）")

    private BigDecimal fixationPrice;

    @ApiModelProperty(value = "出厂价（固定价格使用）")

    private BigDecimal outFactoryPrice;

    @ApiModelProperty(value = "运杂费（固定价格使用）")

    private BigDecimal transportPrice;

    /**
     * 上下游账期月差
     */
    private BigDecimal monthlyDifference;

    /**
     * 含税拟销售单价
     */
    private  BigDecimal unitPriceIncludingTax;

    /**
     * 含税拟销售金额
     */
    private BigDecimal taxInclusiveAmount;

    private String supplierName;

    private Integer xh;

    private Integer bidState;

    private Integer state;

}
