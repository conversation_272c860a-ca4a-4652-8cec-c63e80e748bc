package scrbg.meplat.mall.dto.bidding;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import scrbg.meplat.mall.entity.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @program: maill_api
 *
 * @description: 报价明细数据对象
 *
 * @author: 代文翰
 *
 * @create: 2023-07-20 16:51
 **/
@Data
public class BidOfferVo {
    @ApiModelProperty(value = "竞价记录id")
    private String bidRecordId;
    @ApiModelProperty(value = "竞价采购编号")
    private String biddingSn;
    @ApiModelProperty(value = "标题")

    private String title;
    @ApiModelProperty(value = "价格类型（1浮动价格2固定价格）大宗临购使用")

    private Integer billType;
    @ApiModelProperty(value = "商品类型：0物资 （所有商品都是物资，只有下单才会根据分类自动生成不同的订单）最新改动：商品类型：0 低值易耗品 1大宗临购")

    private Integer productType;
    @ApiModelProperty(value = "公示状态（0未发布1已发布）")

    private Integer publicityState;
    @ApiModelProperty(value = "创建机构名称")

    private String createOrgName;

    @ApiModelProperty(value = "发布时间")

    private Date startTime;

    @ApiModelProperty(value = "截止时间")
    private Date endTime;

    @ApiModelProperty(value = "不含税总金额")

    private BigDecimal bidAmount;
    @ApiModelProperty(value = "含税总金额")

    private BigDecimal bidRateAmount;
    @ApiModelProperty(value = "报价状态")

    private Integer status;
    @ApiModelProperty(value = "报价编辑状态")

    private Integer editAble;

    @ApiModelProperty(value = "竞价明细")

    private List<BidOfferInfoVo> productList = new ArrayList<>();
    @ApiModelProperty(value = "报价文件")
    private File bidFile;
    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;
    @ApiModelProperty(value = "竞价函说明")
    private String biddingNotice;

}
