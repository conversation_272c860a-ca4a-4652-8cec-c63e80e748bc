package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import scrbg.meplat.mall.dto.bidding.BatchUpdateBiddingItemInfoDTO;
import scrbg.meplat.mall.dto.bidding.BidOfferInfoVo;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：竞价采购表
 * @作者: ye
 * @日期: 2023-07-19
 */
@ApiModel(value = "竞价采购表")
@Data
@TableName("bidding_purchase")
public class BiddingPurchase extends MustBaseEntity implements Comparable<BiddingPurchase>,Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "竞价采购id")
    private String biddingId;

    @ApiModelProperty(value = "竞价采购编号")

    private String biddingSn;


    @ApiModelProperty(value = "竞价来源类型（1订单2商品（物资基础库）3清单）")

    private Integer biddingSourceType;


    @ApiModelProperty(value = "标题")

    private String title;


    @ApiModelProperty(value = "竞价采购类型（1公开竞价2邀请竞价）")

    private Integer type;


    @ApiModelProperty(value = "发布时间")

    private Date startTime;

    @ApiModelProperty(value = "截止时间")

    private Date endTime;

    @ApiModelProperty(value = "延长截止时间原因")
    private String deadlineTimeResult;

    @ApiModelProperty(value = "联系人名称")

    private String linkName;


    @ApiModelProperty(value = "联系电话")

    private String linkPhone;


    @ApiModelProperty(value = "竞价说明")

    private String biddingExplain;
    @ApiModelProperty(value = "竞价函说明")
    private String biddingNotice;


    @ApiModelProperty(value = "状态（0待提交1待审核2审核失败5审核通过   4：开标  6中标待审核7已中标8中标审核失败9已流标")

    private Integer state;


    @ApiModelProperty(value = "店铺id")

    private String shopId;


    @ApiModelProperty(value = "时间状态（1未开始2进行中3已结束）")

    private Integer biddingState;


    @ApiModelProperty(value = "公示状态（0未发布1已发布）暂时废弃")

    private Integer publicityState;

    @ApiModelProperty(value = "创建机构id")

    private String createOrgId;

    @ApiModelProperty(value = "创建机构名称")

    private String createOrgName;



    @ApiModelProperty(value = "是否中标（0否1是）")

    private Integer isHitBidding;


    @ApiModelProperty(value = "商品类型：0物资 （所有商品都是物资，只有下单才会根据分类自动生成不同的订单）最新改动：商品类型：0 低值易耗品 1大宗临购 2、周转材料")

    private Integer productType;

    @ApiModelProperty(value = "价格类型（1浮动价格2固定价格）大宗临购使用")

    private Integer billType;

    @ApiModelProperty(value = "是否提交审核")
    @TableField(exist = false)
    private Integer isSubmit;

    @ApiModelProperty(value = "修改竞价明细")
    @TableField(exist = false)
    List<BatchUpdateBiddingItemInfoDTO> dtos;
    @ApiModelProperty(value = "大宗临购单编号")

    @TableField(exist = false)
    private String deadlineTime;

    /**
     * 竞价记录 --前端使用
     */
    @TableField(exist = false)
    private List<BidOfferInfoVo>  bidOfferInfoVos;

    /**
     * 前端使用，1：新增竞价；2：竞价记录-审核；3：竞价记录-撤回；4：竞价记录-作废
     */
    @TableField(exist = false)
    private Integer pageType;

    @TableField(exist = false)
    private String recordId;

    /**
     * 1:第一条；2：非第一条
     */
    @TableField(exist = false)
    private Integer auditProcessType;

    /**
     * 通过公司
     */
    @TableField(exist = false)
    private String  biddingOrderCompany;


    private String synthesizeTemporarySn;
    public Integer currentCount(){
        // 提取所有数字
        String digits = this.biddingSn.replaceAll("\\D", "");

        // 如果数字长度超过3位，则取后三位；否则，直接返回所有数字
        String lastThreeDigits = digits.length() > 3 ? digits.substring(digits.length() - 3) : digits;

        // 将最后三位转为整数返回
        return Integer.parseInt(lastThreeDigits);
        //return Integer.parseInt(this.biddingSn.replaceAll("\\D", ""));
    }

    @Override
    public int compareTo(@NotNull BiddingPurchase biddingPurchase) {
        int thisSn = Integer.parseInt(this.biddingSn.replaceAll("\\D", ""));
        int currentSn = Integer.parseInt(biddingPurchase.getBiddingSn().replaceAll("\\D", ""));
        return Integer.compare(currentSn,thisSn);
    }
}