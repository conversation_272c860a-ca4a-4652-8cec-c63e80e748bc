package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.BiddingBidRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mall.vo.bidding.BiddingBidRecordVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @描述：竞价记录 服务类
 * @作者: ye
 * @日期: 2023-07-11
 */
public interface BiddingBidRecordService extends IService<BiddingBidRecord> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingBidRecord> queryWrapper);

        void create(BiddingBidRecord biddingBidRecord);

        void update(BiddingBidRecord biddingBidRecord);

        BiddingBidRecord getById(String id);

        void delete(String id);

        /**
         * 根据竞价id查询所有的竞价记录
         * @param biddingId
         * @return
         */
    List<BiddingBidRecord> selectListByBiddingId(String biddingId);

    /**
     * 竞价结束之后导出汇总报价表
     * @param bidSn
     * @param response
     */
    void exportBidSummary(String bidSn, HttpServletResponse response);

    BiddingBidRecordVO getBiddingRecordInfo(String bidRecordSn);

}
