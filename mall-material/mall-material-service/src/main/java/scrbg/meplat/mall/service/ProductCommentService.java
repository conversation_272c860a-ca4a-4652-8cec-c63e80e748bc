package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.ProductComment;
import scrbg.meplat.mall.vo.product.ProductDetailCommentPageListVO;

import java.util.Date;
import java.util.List;

/**
 * @描述：商品评价 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface ProductCommentService extends IService<ProductComment> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProductComment> queryWrapper);

    void create(ProductComment productComment);

    void update(ProductComment productComment);

    ProductComment getById(String id);

    void delete(String id);

    /**
     * 删除评价
     * @param orderItemId
     */
    void deleteComment(String orderItemId);

    /**
     * 修改评价
     * @param productComment
     */
    void updateComment(ProductComment productComment);

    /**
     * 获取评价根据订单项id
     * @param orderItemId
     * @return
     */
    ProductComment getCommentByOrderItemId(String orderItemId);

    ProductComment getCommentByOrderId(String orderItemId);

    /**
     * 补充评价信息列表
     * @param dtos
     * @return
     */
    List<ProductDetailCommentPageListVO> getCommentListUserInfo(List<ProductDetailCommentPageListVO> dtos);

    /**
     *通过店铺id,评价时间查询订单评价
     */
    List<ProductComment> getProductCommentByShopId(String shopId, Date startDate, Date endDate);
}
