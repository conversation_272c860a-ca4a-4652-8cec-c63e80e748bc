package scrbg.meplat.mall.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.AuditRecords;
import scrbg.meplat.mall.entity.ProcessInstance;
import scrbg.meplat.mall.entity.ProcessRole;
import scrbg.meplat.mall.mapper.ProcessNodeOperationMapper;
import scrbg.meplat.mall.service.AuditService;
import scrbg.meplat.mall.service.ProcessInstanceService;
import scrbg.meplat.mall.service.ProcessNodeService;
import scrbg.meplat.mall.service.ProcessRoleService;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AuditServiceImpl implements AuditService {

    @Autowired
    ProcessNodeService processNodeService;

    @Autowired
    ProcessInstanceService processInstanceService;

    @Autowired
    ProcessNodeOperationMapper processNodeMapper;

    @Autowired
    ProcessRoleService processRoleService;

    @Override
    public List<AuditRecords> findRecordsBy(String processInstanceId) {

        ProcessInstance instance = processInstanceService.getById(processInstanceId);
        if (instance == null) return Collections.emptyList();
        List<AuditRecords> listByInstanceId = processNodeMapper.findListByInstanceId(processInstanceId);
        if (listByInstanceId.isEmpty()) return Collections.emptyList();
        List<AuditRecords> collect = listByInstanceId.stream().peek(e -> {
            List<ProcessRole> list = processRoleService.lambdaQuery().eq(ProcessRole::getProcessNodeId, e.getNodeId()).list();
            if (!list.isEmpty()) {
                ProcessRole processRole = list.get(0);
                e.setReviewSecondary(processRole.getRoleName());
            }
        }).collect(Collectors.toList());
        return collect;

    }
}
