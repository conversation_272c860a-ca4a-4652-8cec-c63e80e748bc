package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.scrbg.common.utils.PageUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import scrbg.meplat.mall.common.Query;
import scrbg.meplat.mall.common.constant.ProcessConstants;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.bidding.BidOfferInfoVo;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.BiddingBidRecordMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.bidding.BidSummarySubVo;
import scrbg.meplat.mall.vo.bidding.BiddingBidRecordVO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @描述：竞价记录 服务类
 * @作者: ye
 * @日期: 2023-07-11
 */
@Service
public class BiddingBidRecordServiceImpl extends ServiceImpl<BiddingBidRecordMapper, BiddingBidRecord> implements BiddingBidRecordService {
    @Resource
    private BiddingProductService biddingProductService;
    @Autowired
    private MallConfig mallConfig;
    @Resource
    private BiddingPurchaseService biddingPurchaseService;
    @Resource
    private SynthesizeTemporaryService synthesizeTemporaryService;

    @Autowired
    BiddingBidRecordMapper bidRecordMapper;

    @Autowired
    ProcessConfigService processConfigService;

    @Autowired
    ProcessInstanceService processInstanceService;

    @Autowired
    AuditService auditService;

    @Autowired
    BiddingBidRecordItemService recordItemService;

    @Autowired
    private BiddingWinRecordService biddingWinRecordService;

    @Autowired
    FileService fileService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingBidRecord> queryWrapper) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        queryWrapper.eq(BiddingBidRecord::getSupplierId, user.getEnterpriseId());
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String supplierName = (String) innerMap.get("supplierName");
        String biddingId = (String) innerMap.get("biddingId");
        Integer state = (Integer) innerMap.get("state");
        BigDecimal tallbidPrice = (BigDecimal) innerMap.get("tallbidPrice");
        BigDecimal endbidPrice = (BigDecimal) innerMap.get("lowbidPrice");

        String keywords = (String) innerMap.get("keywords");
        String staBidTime = (String) innerMap.get("staBidTime");
        String endBidTime = (String) innerMap.get("endBidTime");
        queryWrapper.eq(supplierName != null, BiddingBidRecord::getSupplierName, supplierName);
//            queryWrapper.gt(tallbidPrice!=null,BiddingBidRecord::getBidPrice,tallbidPrice);
//            queryWrapper.lt(endbidPrice!=null,BiddingBidRecord::getBidPrice,endbidPrice);
//            queryWrapper.lt(endbidPrice!=null,BiddingBidRecord::getBidPrice,endbidPrice);
        queryWrapper.lt(biddingId != null, BiddingBidRecord::getBiddingId, biddingId);
        if (staBidTime != null && staBidTime != "") {
            queryWrapper.gt(BiddingBidRecord::getBidTime, staBidTime);
        }
        if (endBidTime != null && endBidTime != "") {
            queryWrapper.lt(BiddingBidRecord::getBidTime, endBidTime);
        }
        if (state != null) {
            queryWrapper.eq(BiddingBidRecord::getState, state);
        }
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(BiddingBidRecord::getSupplierName, keywords);
            });
        }

//
//            IPage<BiddingBidRecord> page = this.page(
//        new Query<BiddingBidRecord>().getPage(jsonObject),
//        queryWrapper
//        );
        Page<BiddingBidRecord> page = new Query<BiddingBidRecord>(innerMap).getPage();
        List<BiddingBidRecord> biddingBidRecords = bidRecordMapper.selectListPaging(page, innerMap);
        page.setRecords(biddingBidRecords);
        return new PageUtils(page);
    }

    @Override
    public void create(BiddingBidRecord biddingBidRecord) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingBidRecord);
    }

    @Override
    public void update(BiddingBidRecord biddingBidRecord) {
        super.updateById(biddingBidRecord);
    }


    @Override
    public BiddingBidRecord getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public List<BiddingBidRecord> selectListByBiddingId(String biddingId) {
        LambdaQueryWrapper<BiddingBidRecord> eq = new LambdaQueryWrapper<BiddingBidRecord>().eq(BiddingBidRecord::getBiddingId, biddingId);
        List<BiddingBidRecord> list = list(eq);
        return list;
    }

    @Override
    public BiddingBidRecordVO getBiddingRecordInfo(String bidRecordSn) {
        BiddingBidRecord bidRecord = lambdaQuery().eq(BiddingBidRecord::getBidRecordSn, bidRecordSn).one();
        BiddingPurchase biddingPurchase = biddingPurchaseService.getById(bidRecord.getBiddingId());

        File file = fileService.lambdaQuery()
                .eq(File::getRelevanceId, bidRecord.getBidRecordId())
                .eq(File::getRelevanceType, 15).one();
        if (file != null) {
            bidRecord.setFileFarId(file.getFileFarId());
            bidRecord.setFileName(file.getName());
        }
        BiddingBidRecordVO vo = new BiddingBidRecordVO();
        // 获取主体信息
        BeanUtils.copyProperties(biddingPurchase, vo);


        List<AuditRecords> auditRecords = getAuditRecords(bidRecord);
        vo.setAuditRecords(auditRecords);

        List<BidOfferInfoVo> productList = new ArrayList<>();
        List<BiddingBidRecordItem> itemList = recordItemService.lambdaQuery()
                .eq(BiddingBidRecordItem::getBidRecordId, bidRecord.getBidRecordId())
                .orderByDesc(BiddingBidRecordItem::getGmtModified).list();

        //根据明细表的竞价采购商品ID 查询竞价商品表的商品数据
        for (BiddingBidRecordItem item : itemList) {
            BidOfferInfoVo info = new BidOfferInfoVo();
            info.setSupplierName(bidRecord.getSupplierName());
            // 组装竞价明细
            info.setBidPrice(item.getBidPrice());
            info.setState(bidRecord.getState());
            info.setBidState(0);
            info.setRemarks(item.getRemarks());
            info.setTaxRate(item.getTaxRate());
            info.setBidRatePrice(item.getBidRatePrice());
            info.setBidRateAmount(item.getBidRateAmount());
            info.setBidAmount(item.getBidAmount());
            //info.setRemarks(item.getRemarks());
            String productId = item.getBiddingProductId();
            // 组装物资明细
            BiddingProduct product = biddingProductService.lambdaQuery()
                    .eq(BiddingProduct::getBiddingProductId, productId)
                    //.eq(BiddingProduct::getBiddingSn,biddingSn)
                    .one();
            info.setDeliveryAddress(product.getDeliveryAddress());
            Date deliveryDate = product.getDeliveryDate();
            if (deliveryDate == null) {
                info.setDeliveryDate(null);
            } else {
                info.setDeliveryDate(DateUtil.getyyymmdd(deliveryDate));
            }
            info.setUnit(product.getUnit());
            info.setProductName(product.getProductName());
            info.setSpec(product.getSpec());
            info.setBiddingProductId(product.getBiddingProductId());
            info.setProductTexture(product.getProductTexture());
            info.setNum(product.getNum());
            // 限价
            info.setReferencePrice(product.getReferencePrice());
            // 大宗商品
            if (bidRecord.getProductType() == 1 || bidRecord.getProductType() == 2) {
                if (bidRecord.getBillType() == 1) {
                    // 浮动
                    info.setNetPrice(item.getNetPrice());
                    info.setFixationPrice(item.getFixationPrice());
                }
                if (bidRecord.getBillType() == 2) {
                    // 固定
                    info.setOutFactoryPrice(item.getOutFactoryPrice());
                    info.setTransportPrice(item.getTransportPrice());
                }
            }
            productList.add(info);
        }


        Map<String, Object> map = new ConcurrentHashMap<>();
        List<BidOfferInfoVo> productListSort = new ArrayList<>();
        Map<String, List<BidOfferInfoVo>> groupByGrade = productList.stream()
                .collect(Collectors.groupingBy(BidOfferInfoVo::getSupplierName));
        int xh = 0;

        for (String key : groupByGrade.keySet()) {
            ++xh;
            BidOfferInfoVo bidOfferInfoVo = new BidOfferInfoVo();
            List<BidOfferInfoVo> bidOfferInfoVos = groupByGrade.get(key);
            map.put("materialsNum", groupByGrade.get(key).size());
            int finalXh = xh;
            List<BidOfferInfoVo> collect = bidOfferInfoVos.stream().peek(e -> {
                e.setXh(finalXh);
            }).collect(Collectors.toList());
            double sum = collect.stream()
                    .mapToDouble(e -> e.getBidRateAmount().doubleValue())
                    .sum();
            bidOfferInfoVo.setProductName("小计");
            bidOfferInfoVo.setSpec(sum + "");
            collect.add(bidOfferInfoVo);
            productListSort.addAll(collect);

        }
        map.put("materialsData", productListSort);
        vo.setBiddingBidRecordsMap(map);


        List<BiddingWinRecord> winRecords = biddingWinRecordService.lambdaQuery().eq(BiddingWinRecord::getBiddingId, bidRecord.getBiddingId()).list();
        List<BidOfferInfoVo> winningBidResult = new ArrayList();
        if (winRecords.size() > 0) {
            BiddingWinRecord biddingWinRecord = winRecords.get(0);
            BiddingBidRecord biddingBidRecord = this.getById(biddingWinRecord.getBidRecordId());

            List<BiddingBidRecordItem> itemList1 = recordItemService.lambdaQuery()
                    .eq(BiddingBidRecordItem::getBidRecordId, biddingBidRecord.getBidRecordId())
                    .orderByDesc(BiddingBidRecordItem::getGmtModified).list();

            //根据明细表的竞价采购商品ID 查询竞价商品表的商品数据
            for (BiddingBidRecordItem item : itemList1) {
                BidOfferInfoVo info = new BidOfferInfoVo();
                info.setSupplierName(biddingBidRecord.getSupplierName());
                // 组装竞价明细
                info.setBidPrice(item.getBidPrice());
                info.setBidState(biddingBidRecord.getState());
                info.setRemarks(item.getRemarks());
                info.setTaxRate(item.getTaxRate());
                info.setBidRatePrice(item.getBidRatePrice());
                info.setBidRateAmount(item.getBidRateAmount());
                info.setBidAmount(item.getBidAmount());
                //info.setRemarks(item.getRemarks());
                String productId = item.getBiddingProductId();
                // 组装物资明细
                BiddingProduct product = biddingProductService.lambdaQuery()
                        .eq(BiddingProduct::getBiddingProductId, productId)
                        //.eq(BiddingProduct::getBiddingSn,biddingSn)
                        .one();
                info.setDeliveryAddress(product.getDeliveryAddress());
                Date deliveryDate = product.getDeliveryDate();
                if (deliveryDate == null) {
                    info.setDeliveryDate(null);
                } else {
                    info.setDeliveryDate(DateUtil.getyyymmdd(deliveryDate));
                }
                info.setUnit(product.getUnit());
                info.setProductName(product.getProductName());
                info.setSpec(product.getSpec());
                info.setBiddingProductId(product.getBiddingProductId());
                info.setProductTexture(product.getProductTexture());
                info.setNum(product.getNum());
                // 限价
                info.setReferencePrice(product.getReferencePrice());
                // 大宗商品
                if (bidRecord.getProductType() == 1 || bidRecord.getProductType() == 2) {
                    if (bidRecord.getBillType() == 1) {
                        // 浮动
                        info.setNetPrice(item.getNetPrice());
                        info.setFixationPrice(item.getFixationPrice());
                    }
                    if (bidRecord.getBillType() == 2) {
                        // 固定
                        info.setOutFactoryPrice(item.getOutFactoryPrice());
                        info.setTransportPrice(item.getTransportPrice());
                    }
                }
                winningBidResult.add(info);
            }
        }

        vo.setWinningBidResult(winningBidResult);

        return vo;
    }

    private List<AuditRecords> getAuditRecords(BiddingBidRecord biddingPurchase) {

        List<AuditRecords> auditRecordsList = new ArrayList<>();
        switch (biddingPurchase.getState()) {
            case 0:
            case 1:
            case 2:
            case 5:
                ProcessConfig processConfig = processConfigService.getById(ProcessConstants.PUBLISH_BIDDING_PROCESS_ID);
                List<ProcessInstance> processInstances = processInstanceService.lambdaQuery().eq(ProcessInstance::getProcessId, processConfig.getProcessId()).list();
                if (!processInstances.isEmpty()) {
                    ProcessInstance instance = processInstances.get(0);
                    List<AuditRecords> records = auditService.findRecordsBy(instance.getProcessInstanceId());
                    auditRecordsList.addAll(records);
                }
                break;
            case 4:
            case 6:
            case 7:
            case 8:
            case 9:
                ProcessConfig processConfigFirst = processConfigService.getById(ProcessConstants.BIDDING_PROCESS_ID_FIRST);
                List<ProcessInstance> processInstancesFirst = processInstanceService.lambdaQuery().eq(ProcessInstance::getProcessId, processConfigFirst.getProcessId()).list();
                if (!processInstancesFirst.isEmpty()) {
                    ProcessInstance instance = processInstancesFirst.get(0);
                    List<AuditRecords> records = auditService.findRecordsBy(instance.getProcessInstanceId());
                    auditRecordsList.addAll(records);
                }
                break;
        }
        return auditRecordsList;

    }

    @Override
    public void exportBidSummary(String bidSn, HttpServletResponse response) {
        if (StringUtils.isBlank(bidSn)) {
            throw new BusinessException("未携带竞价编号");
        }
        Map<String, Object> data = new HashMap<>();
        // 竞价数据
        BiddingPurchase purchase = biddingPurchaseService.lambdaQuery()
                .eq(BiddingPurchase::getBiddingSn, bidSn)
                .and(wra -> {
                    wra.eq(BiddingPurchase::getBiddingState, 3).or(rapper -> {
                        rapper.lt(BiddingPurchase::getEndTime, new Date());
                    });
                })
                .one();
        if (ObjectUtils.isEmpty(purchase)) {
            throw new BusinessException("竞价未结束，无法导出");
        }
        // 中标待审核的记录 ？ 是否
        BiddingBidRecord record = super.lambdaQuery().eq(BiddingBidRecord::getBiddingSn, bidSn).eq(BiddingBidRecord::getState, 5).one();
        String hitSupplier = null;
        if (ObjectUtils.isEmpty(record)) {
            //throw new BusinessException("未选择中标供货商");
            hitSupplier = "";

        } else {
            hitSupplier = record.getSupplierName();
        }
        data.put("hitSupplier", hitSupplier);
        // 中标供货商

        data.put("title", purchase.getCreateOrgName());
        data.put("bidSn", purchase.getBiddingSn());
        // 时间
        Date endTime = purchase.getEndTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String endStr = format.format(endTime);
        String[] strings = endStr.split("-");
        data.put("year", strings[0]);
        data.put("month", strings[1]);
        data.put("day", strings[2]);
        // 报价数据
        List<BidSummarySubVo> vos = super.baseMapper.getBidSummaryWithBidRecordId(bidSn);
        List<BidSummarySubVo> newVos = new ArrayList<>();
        Map<String, List<BidSummarySubVo>> collect = vos.stream().collect(Collectors.groupingBy(BidSummarySubVo::getSupplierName));
        collect.forEach((supplier, list) -> {
            // 循环放入
            newVos.addAll(list);
            BigDecimal amount = new BigDecimal(0);
            for (BidSummarySubVo bidSummarySubVo : list) {
                BigDecimal bidRateAmount = bidSummarySubVo.getBidRateAmount();
                amount = amount.add(bidRateAmount);
            }
            BidSummarySubVo bidSummarySubVo = new BidSummarySubVo();
            bidSummarySubVo.setSupplierName(supplier);
            bidSummarySubVo.setProductName("小计");
            bidSummarySubVo.setBidRateAmount(amount);
            newVos.add(bidSummarySubVo);
        });
        ArrayList<BidSummarySubVo> list = Lists.newArrayList(newVos);
        data.put("dataList", list);
        //String src = "/Volumes/westDisk/work/template/竞价模板";
        String src = mallConfig.templateFormUrl;
        if (purchase.getBiddingSourceType() == 3) {
            // 项目部名称
            String synthesizeTemporarySn = purchase.getSynthesizeTemporarySn();
            SynthesizeTemporary synthesizeTemporary = synthesizeTemporaryService.lambdaQuery().eq(SynthesizeTemporary::getSynthesizeTemporarySn, synthesizeTemporarySn).select(SynthesizeTemporary::getOrgName).one();
            if (synthesizeTemporary != null) {
                data.put("title", synthesizeTemporary.getOrgName());
            }
            try {
                ExcelForWebUtil.exportExcel(response, data, "大宗临购清单竞价汇总模板.xlsx", src, purchase.getTitle() + "竞价汇总.xlsx");
                return;
            } catch (Exception e) {
                throw new BusinessException("导出异常" + e.getMessage());
            }
        }
        try {
            ExcelForWebUtil.exportExcel(response, data, "竞价汇总表模板.xlsx", src, purchase.getTitle() + "竞价汇总.xlsx");
        } catch (Exception e) {
            throw new BusinessException("导出异常" + e.getMessage());
        }
    }
}
