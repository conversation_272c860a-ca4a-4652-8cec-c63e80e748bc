package scrbg.meplat.mall.service.stockManage;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.InboundSettlementManage;
import scrbg.meplat.mall.entity.SelfOperatedStoreRecord;

import javax.servlet.http.HttpServletResponse;

public interface InboundSettlementService {
    void saveSettlement(InboundSettlementManage inboundSettlementManage);
    void updateSettlement(InboundSettlementManage inboundSettlementManage);

    PageUtils<InboundSettlementManage> queryPage(JSONObject jsonObject, LambdaQueryWrapper<InboundSettlementManage> queryWrapper);

    void saveAndSubmitSettlement(InboundSettlementManage inboundSettlementManage);

    void export(String reconciliationId, HttpServletResponse response);

    void updateState(String id, int state);
}
