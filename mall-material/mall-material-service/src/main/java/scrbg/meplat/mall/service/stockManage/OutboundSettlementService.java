package scrbg.meplat.mall.service.stockManage;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.InboundSettlementManage;
import scrbg.meplat.mall.entity.OutboundSettlementManage;

import javax.servlet.http.HttpServletResponse;

public interface OutboundSettlementService {

    void saveSettlement(OutboundSettlementManage outboundSettlement);

    PageUtils<OutboundSettlementManage> queryPage(JSONObject jsonObject, LambdaQueryWrapper<OutboundSettlementManage> queryWrapper);


    void saveAndSubmitSettlement(OutboundSettlementManage outboundSettlementManage);

    void export(String id, HttpServletResponse response);

    void updateState(String id, int state);
}
