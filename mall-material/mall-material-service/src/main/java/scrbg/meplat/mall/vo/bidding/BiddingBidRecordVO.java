package scrbg.meplat.mall.vo.bidding;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.dto.bidding.BidOfferInfoVo;
import scrbg.meplat.mall.entity.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class BiddingBidRecordVO extends BiddingBidRecord {

    /**
     * 经办人ID
     */
    private String operatorId;


    /**
     * 审核记录
     */
    private List<AuditRecords> auditRecords;

    /**
     * 报价记录
     */
    private Map<String,Object> biddingBidRecordsMap;


    /**
     * 中标结果
     */
    private List<BidOfferInfoVo> winningBidResult;
}
