package scrbg.meplat.mall.vo.bidding;

import lombok.Data;
import scrbg.meplat.mall.dto.bidding.BidOfferInfoVo;
import scrbg.meplat.mall.entity.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-07-20 16:37
 */
@Data
public class BiddingPurchaseInfoVO extends BiddingPurchase {

    /**
     * 竞价明细
     */
    private List<BiddingProduct> biddingProducts;

    /**
     * 审核记录
     */
    private List<AuditRecords> auditRecords;

    /**
     * 报价记录
     */
    private Map<String,Object> biddingBidRecordsMap;

    /**
     * 供应商参与
     */
    private List<BiddingSuppliers> biddingSuppliers;
    /**
     * 邀请供应商列表
     */
    private List<BiddingInvitationRelevance> biddingInviteSuppliers;

    /**
     * 中标结果
     */
    private List<BidOfferInfoVo> winningBidResult;
}
