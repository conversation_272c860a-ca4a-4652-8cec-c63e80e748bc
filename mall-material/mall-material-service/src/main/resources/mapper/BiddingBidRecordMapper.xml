<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.BiddingBidRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.BiddingBidRecord" id="BiddingBidRecordMap">
        <result property="bidRecordId" column="bid_record_id"/>
        <result property="biddingId" column="bidding_id"/>
        <result property="biddingSn" column="bidding_sn"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="contactPerson" column="contact_person"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="bidAmount" column="bid_amount"/>
        <result property="bidRateAmount" column="bid_rate_amount"/>
        <result property="bidTime" column="bid_time"/>
        <result property="bidingRemarks" column="biding_remarks"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>
    <select id="getBidSummaryWithBidRecordId" resultType="scrbg.meplat.mall.vo.bidding.BidSummarySubVo">
        SELECT
            record.supplier_id,
            record.supplier_name,
            product.product_name,
            product.product_texture,
            product.spec,
            product.unit,
            product.num,
            delivery_date,
            item.bid_price,
            item.bid_amount,
            item.bid_rate_price,
            item.bid_rate_amount,
            item.remarks
        FROM
            bidding_bid_record record
                JOIN bidding_bid_record_item item ON record.bid_record_id = item.bid_record_id
                INNER JOIN bidding_product product ON item.bidding_product_id = product.bidding_product_id
                AND item.is_delete = 0
                AND product.is_delete = 0
                AND record.state > 0
        WHERE
            record.bidding_sn = #{bSn};

    </select>


    <select id="selectListPaging" resultType="scrbg.meplat.mall.entity.BiddingBidRecord">
        select t1.*,t2.title,t2.type,t2.end_time,t2.product_type,t2.bidding_source_type,t2.start_time from bidding_bid_record t1 left join bidding_purchase t2 on t1.bidding_id = t2.bidding_id
        <where>
            <if test="params.title != null  and params.title != ''">
                and t2.title like concat('%', #{params.title}, '%')
            </if>

            <if test="params.gmtCreate != null">
                and t1.gmt_create = #{params.gmtCreate}
            </if>
            <if test="params.startTime != null">
                and t1.start_time = #{params.startTime}
            </if>
            <if test="params.endTime != null">
                and t1.end_time = #{params.endTime}
            </if>
        </where>
        <if test="params.orderBy != null and params.orderBy == 1">
            ORDER BY t1.gmt_create DESC
        </if>
        <if test="params.orderBy != null and params.orderBy == 2">
            ORDER BY t2.start_time DESC
        </if>
        <if test="params.orderBy != null and params.orderBy == 3">
            ORDER BY t2.end_time DESC
        </if>
<!--        ORDER BY t1.gmt_modified DESC-->
    </select>


</mapper>