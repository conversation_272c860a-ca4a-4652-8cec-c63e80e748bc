<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ProductCommentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ProductComment" id="ProductCommentMap">
        <result property="commentId" column="comment_id"/>
        <result property="productId" column="product_id"/>
        <result property="userId" column="user_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="productName" column="product_name"/>
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="commentType" column="comment_type"/>
        <result property="commentLevel" column="comment_level"/>
        <result property="commentContent" column="comment_content"/>
        <result property="commentImgs" column="comment_imgs"/>
        <result property="evaluateTime" column="evaluate_time"/>
        <result property="isReply" column="is_reply"/>
        <result property="replyContent" column="reply_content"/>
        <result property="replyTime" column="reply_time"/>
        <result property="isShow" column="is_show"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="productType" column="product_type"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>


</mapper>