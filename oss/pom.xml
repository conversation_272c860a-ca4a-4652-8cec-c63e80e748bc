<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.scrbg.maill_api</groupId>
        <artifactId>maill_api</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <groupId>com.y</groupId>
    <artifactId>oss</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>oss</name>
    <description>oss</description>
    <properties>
        <java.version>8</java.version>
    </properties>
    <dependencies>
        <!--分布式存储-->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.4.0</version>
        </dependency>
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.14</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.30</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.11.0</version>
        </dependency>
        <!--common-->
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--mybatis plus引用-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>dev</id>
            <properties>
                <activeProfile>dev</activeProfile>
                <skipDocker>true</skipDocker>
                <dockerHost>http://***************:2375</dockerHost>
            </properties>
            <!--<activation>
                <activeByDefault>true</activeByDefault>
            </activation>-->
        </profile>
        <profile>
            <!-- 测试环境 -->
            <id>test</id>
            <properties>
                <activeProfile>test</activeProfile>
                <skipDocker>false</skipDocker>
                <dockerHost>http://**************:2375</dockerHost>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 -->
            <id>prod</id>
            <properties>
                <activeProfile>prod</activeProfile>
                <skipDocker>false</skipDocker>
                <dockerHost>http://***************:2375</dockerHost>
            </properties>
        </profile>
    </profiles>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
