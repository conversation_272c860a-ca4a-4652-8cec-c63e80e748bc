spring:
  cloud:
    nacos:
      config:
        enabled: false # 关闭
      discovery: # TODO 这里oss事实上不需要注册到nacos，前端是直连oss服务的（生产环境由nginx反向代理）后续删除？
        server-addr: 192.168.91.16:8849
        username: nacos
        password: 9ImAtEIE
  datasource:
    username: root
    password: 9ImAtEIE
    url: ***************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
#接口文档
knife4j:
  enable: true
  setting:
    enableSwaggerModels: true
    swaggerModelName: 实体类列表
minio:
  endpoint: http://192.168.91.16:9002 #Minio服务所在地址
  accessKey: minioadmin #访问的key
  secretKey: 9ImAtEIE #访问的秘钥
  cluster: false  # 设置为 true 表示使用集群模式

