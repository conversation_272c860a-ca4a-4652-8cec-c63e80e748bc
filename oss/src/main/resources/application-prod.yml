spring:
  cloud:
    nacos:
      config:
        enabled: false # 关闭
      discovery:
        server-addr: *************:8848
        username: nacos # Nacos 用户名
        password: 9ImAtEIE # Nacos 密码
        register-enabled: true
        ip: *************
  datasource:
    username: root
    password: 9ImAtEIE
    url: ***************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
#接口文档
knife4j:
  enable: false
  setting:
    enableSwaggerModels: false
    swaggerModelName: 实体类列表
minio:
  endpoint: http://*************:9000 #Minio服务所在地址
  accessKey: minio #访问的key
  secretKey: 9ImAtEIE #访问的秘钥
