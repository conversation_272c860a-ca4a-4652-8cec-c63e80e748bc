server:
  port: 9009
  tomcat:
    uri-encoding: UTF-8
    max-swallow-size: -1 #内置tomcat限制文件最大大小 -1为不限制
    connection-timeout: 1800000
seata:
  enabled: false
spring:
  #管理文件上传和请求数据大小
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB  #单文件最大限制
      max-request-size: 500MB #总文件最大限制
      file-size-threshold: 500MB
  main:
    allow-bean-definition-overriding: true
  application:
    name: mall-oss
  #本地环境
  profiles:
    active: "dev"
mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      logic-delete-field: isDelete #默认deleted
      logic-delete-value: -1
      logic-not-delete-value: 0
      id-type: auto













