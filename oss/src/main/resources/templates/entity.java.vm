package ${package.Entity};

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
/**
 * @描述：$!{table.comment}
 * @作者: ${author}
 * @日期: ${date}
 */
@ApiModel(value="$!{table.comment}")
@Data
@TableName("${table.name}")
public class ${entity} extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    #if(${field.keyFlag})
        #set($keyPropertyName=${field.propertyName})
    #end
    #if($!field.propertyName.indexOf('Id') != -1 && ${foreach.index} == '0')
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "${field.comment}")
    private ${field.propertyType} ${field.propertyName};#end
    #if("$!field.comment" != "")
        #if(${field.propertyName}=="sort"||${field.propertyName}=="mallType"||${field.propertyName}=="gmtCreate"
        ||${field.propertyName}=="gmtModified"||${field.propertyName}=="founderName"
        ||${field.propertyName}=="founderId"||${field.propertyName}=="remarks"
        ||${field.propertyName}=="isDelete"||($!field.propertyName.indexOf('Id') != -1 && ${foreach.index} == '0'))
        #else
    @ApiModelProperty(value = "${field.comment}")

    private ${field.propertyType} ${field.propertyName};
        #end
    #end

##    #if(${field.propertyType}=="Blob")
##        private byte[] ${field.propertyName};
##    #else
##        private ${field.propertyType} ${field.propertyName};
##    #end
##    private ${field.propertyType} ${field.propertyName}#if(${field.propertyType} == "BigDecimal") = new BigDecimal(0)#end;

#end

}