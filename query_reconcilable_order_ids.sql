-- 查询所有可对账的订单ID
-- 根据指定的供应商、二级供应商、时间范围、来源类型和对账状态条件

SELECT DISTINCT res.order_id
FROM (
    -- 发货数据子查询
    SELECT
        ors.order_id,
        ors.source_type,
        ors.supplier_id AS supplierOrgId,
        ors.confirm_time AS receivingDate,
        ors.ship_enterprise_id AS twoSupplierOrgId,
        dtl.is_reconciliation
    FROM order_ship ors
    INNER JOIN order_ship_dtl dtl ON ors.bill_Id = dtl.bill_id
    WHERE ors.type = 2
        AND ors.order_class != 1
        AND ors.is_delete = 0
        AND dtl.is_delete = 0
    UNION ALL
    -- 退货数据子查询
    SELECT
        ore.order_id,
        ore.source_type,
        ore.supplier_id AS supplierOrgId,
        ore.flish_time AS receivingDate,
        ore.ship_enterprise_id AS twoSupplierOrgId,
        ori.is_reconciliation
    FROM order_return ore
    INNER JOIN order_return_item ori ON ore.order_return_id = ori.order_return_id
    WHERE ore.state = 3
        AND ore.is_out = 1
        AND ore.is_delete = 0
        AND ori.is_delete = 0
) res
WHERE res.supplierOrgId = '1880074382960799747'
    AND res.twoSupplierOrgId = '1663344005459537922'
    AND res.receivingDate > '2021-08-01 00:00:00'
    AND res.receivingDate < '2025-09-30 00:00:00'
    AND res.source_type = 2
    AND res.is_reconciliation = 0
ORDER BY res.order_id;

-- 如果需要统计总数，可以使用以下查询：
SELECT COUNT(DISTINCT res.order_id) as total_count
FROM (
    -- 发货数据子查询
    SELECT
        ors.order_id,
        ors.source_type,
        ors.supplier_id AS supplierOrgId,
        ors.confirm_time AS receivingDate,
        ors.ship_enterprise_id AS twoSupplierOrgId,
        dtl.is_reconciliation
    FROM order_ship ors
    INNER JOIN order_ship_dtl dtl ON ors.bill_Id = dtl.bill_id
    WHERE ors.type = 2
        AND ors.order_class != 1
        AND ors.is_delete = 0
        AND dtl.is_delete = 0
    UNION ALL
    -- 退货数据子查询
    SELECT
        ore.order_id,
        ore.source_type,
        ore.supplier_id AS supplierOrgId,
        ore.flish_time AS receivingDate,
        ore.ship_enterprise_id AS twoSupplierOrgId,
        ori.is_reconciliation
    FROM order_return ore
    INNER JOIN order_return_item ori ON ore.order_return_id = ori.order_return_id
    WHERE ore.state = 3
        AND ore.is_out = 1
        AND ore.is_delete = 0
        AND ori.is_delete = 0
) res
WHERE res.supplierOrgId = '1880074382960799747'
    AND res.twoSupplierOrgId = '1663344005459537922'
    AND res.receivingDate > '2021-08-01 00:00:00'
    AND res.receivingDate < '2025-09-30 00:00:00'
    AND res.source_type = 2
    AND res.is_reconciliation = 0;

-- 如果需要带分页的查询（例如每页100条）：
SELECT DISTINCT res.order_id
FROM (
    -- 发货数据子查询
    SELECT
        ors.order_id,
        ors.source_type,
        ors.supplier_id AS supplierOrgId,
        ors.confirm_time AS receivingDate,
        ors.ship_enterprise_id AS twoSupplierOrgId,
        dtl.is_reconciliation
    FROM order_ship ors
    INNER JOIN order_ship_dtl dtl ON ors.bill_Id = dtl.bill_id
    WHERE ors.type = 2
        AND ors.order_class != 1
        AND ors.is_delete = 0
        AND dtl.is_delete = 0
    UNION ALL
    -- 退货数据子查询
    SELECT
        ore.order_id,
        ore.source_type,
        ore.supplier_id AS supplierOrgId,
        ore.flish_time AS receivingDate,
        ore.ship_enterprise_id AS twoSupplierOrgId,
        ori.is_reconciliation
    FROM order_return ore
    INNER JOIN order_return_item ori ON ore.order_return_id = ori.order_return_id
    WHERE ore.state = 3
        AND ore.is_out = 1
        AND ore.is_delete = 0
        AND ori.is_delete = 0
) res
WHERE res.supplierOrgId = '1880074382960799747'
    AND res.twoSupplierOrgId = '1663344005459537922'
    AND res.receivingDate > '2021-08-01 00:00:00'
    AND res.receivingDate < '2025-09-30 00:00:00'
    AND res.source_type = 2
    AND res.is_reconciliation = 0
ORDER BY res.order_id
LIMIT 100 OFFSET 0;  -- 第一页，每页100条

-- 参数化查询版本（用于MyBatis等ORM框架）：
/*
SELECT DISTINCT res.order_id
FROM (
    SELECT
        ors.order_id,
        ors.source_type,
        ors.supplier_id AS supplierOrgId,
        ors.confirm_time AS receivingDate,
        ors.ship_enterprise_id AS twoSupplierOrgId,
        dtl.is_reconciliation
    FROM order_ship ors
    INNER JOIN order_ship_dtl dtl ON ors.bill_Id = dtl.bill_id
    WHERE ors.type = 2
        AND ors.order_class != 1
        AND ors.is_delete = 0
        AND dtl.is_delete = 0
    UNION ALL
    SELECT
        ore.order_id,
        ore.source_type,
        ore.supplier_id AS supplierOrgId,
        ore.flish_time AS receivingDate,
        ore.ship_enterprise_id AS twoSupplierOrgId,
        ori.is_reconciliation
    FROM order_return ore
    INNER JOIN order_return_item ori ON ore.order_return_id = ori.order_return_id
    WHERE ore.state = 3
        AND ore.is_out = 1
        AND ore.is_delete = 0
        AND ori.is_delete = 0
) res
WHERE res.supplierOrgId = #{supplierOrgId}
    AND res.twoSupplierOrgId = #{twoSupplierOrgId}
    AND res.receivingDate > #{startTime}
    AND res.receivingDate < #{endTime}
    AND res.source_type = #{sourceType}
    AND res.is_reconciliation = 0
ORDER BY res.order_id;
*/
