<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tende</artifactId>
        <groupId>scrbg.meplat.tende</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tende-service</artifactId>

    <dependencies>
        <!--demo接口引用-->
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>demo-interface</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--字典-->
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>config-interface</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--获取姓名或者名称的首字母或者全拼-->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
            <version>3.8.7</version>
        </dependency>
        <!--common-->
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--物资-->
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>material-inferface</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--hr-->
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>hr-interface</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--物资结算-->
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>materialsettle-interface</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--设备交接-->
        <dependency>
            <artifactId>facilityconnect-interface</artifactId>
            <groupId>com.scrbg.pcwp2</groupId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--设备台账-->
        <dependency>
            <artifactId>facilityaccount-interface</artifactId>
            <groupId>org.scrbg.pcwp2</groupId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--设备基础-->
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>demo-interface</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--设备计划-->
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>facilityplan-interface</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--设备日常-->
        <dependency>
            <artifactId>facilitymanagement-interface</artifactId>
            <groupId>com.scrbg.pcwp2</groupId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--招标-->
        <dependency>
            <artifactId>tender-interface</artifactId>
            <groupId>com.scrbg.pcwp2</groupId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--物资管理接口引用-->
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>material-inferface</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--mybatis plus引用-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.2</version>
        </dependency>

        <!--seata引用-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-seata</artifactId>
            <version>2.2.0.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>io.seata</groupId>
                    <artifactId>seata-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>        <!--test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
            <version>1.4.0</version>
        </dependency>
        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
            <version>3.8.7</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>


    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>dev</id>
            <properties>
                <activeProfile>dev</activeProfile>
                <skipDocker>true</skipDocker>
                <dockerHost>http://***************:2375</dockerHost>
            </properties>
            <!--<activation>
                <activeByDefault>true</activeByDefault>
            </activation>-->
        </profile>
        <profile>
            <!-- 测试环境 -->
            <id>test</id>
            <properties>
                <activeProfile>test</activeProfile>
                <skipDocker>false</skipDocker>
                <dockerHost>http://**************:2375</dockerHost>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 -->
            <id>prod</id>
            <properties>
                <activeProfile>prod</activeProfile>
                <skipDocker>false</skipDocker>
                <dockerHost>http://***************:2375</dockerHost>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
