package scrbg.meplat.tende;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.tende.adapter.ApplicationConfig;

import javax.annotation.PostConstruct;

@SpringBootApplication
@EnableFeignClients
@Import(value = {ApplicationConfig.class})
@MapperScan("scrbg.meplat.tende.mapper")
public class TendeSreviceApplication {
@Autowired
public RestTemplateBuilder builder;
@Bean
public RestTemplate restTemplate(){
    return builder.build();
}

    @PostConstruct
    private void init(){
            System.out.println("                     =================================================");
            System.out.println("                     ====================招标     项目==================");
            System.out.println("                     ====================服务器启动成功==================");
            System.out.println("                     =================================================");

        }
    public static void main(String[] args){
        SpringApplication.run(TendeSreviceApplication.class, args);}
}

