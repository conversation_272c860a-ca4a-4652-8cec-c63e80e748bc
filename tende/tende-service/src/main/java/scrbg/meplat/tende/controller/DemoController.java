package scrbg.meplat.tende.controller;


import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.tende.exception.BusinessException;
import scrbg.meplat.tende.service.Demo;
import scrbg.meplat.tende.service.DemoService;

@RestController
@RequestMapping("/demo")
@Api(tags = "实例")
public class DemoController {

    @Autowired
    DemoService demoService;

    @GetMapping("/test")
    @ApiOperation(value = "测试实例")
    public R<Demo> test() {
        try {
            return R.success(demoService.test());
        } catch (BusinessException bex) {
            return R.failed(bex.getCode(), bex.getMessage());
        } catch (Exception e) {
            return R.failed(500, e.getMessage());
        }
    }

}
