package scrbg.meplat.tende.controller;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;

import scrbg.meplat.tende.entity.TenderComment;
import scrbg.meplat.tende.entity.TenderDtl;
import scrbg.meplat.tende.service.TenderCommentService;

import java.util.List;


/**
 * @描述：分包公司评论表（澄清提问）控制类
 * @作者: demo
 * @日期: 2023-02-10
 */
@RestController
@RequestMapping("/tenderComment")
@Api(tags = "分包公司评论表（澄清提问）")
public class TenderCommentController{

@Autowired
public TenderCommentService tenderCommentService;

@PostMapping("/listByEntity")
@ApiOperation(value = " 根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<TenderComment> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= tenderCommentService.queryPage(jsonObject,new LambdaQueryWrapper<TenderComment>());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<TenderComment> findById(String id){
    TenderComment tenderComment = tenderCommentService.getById(id);
        return R.success(tenderComment);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
@ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
public R save(@RequestBody TenderComment tenderComment){
    tenderCommentService.create(tenderComment);
        return R.success();
        }
    @GetMapping("/listByBillId")
    @ApiOperation(value = "根据招标id查询招标清单表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R listByBillId(String id) {
        List<TenderComment> list = tenderCommentService.listByBillId(id);
        return R.success(list);
    }
@PostMapping("/update")
@ApiOperation(value = "修改")
@ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
public R update(@RequestBody TenderComment tenderComment){
    tenderCommentService.update(tenderComment);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    tenderCommentService.delete(id);
        return R.success();
        }

@PostMapping("/saveBatch")
@ApiOperation(value = "批量添加")
public R saveBatch(@RequestBody List<TenderComment> tenderComments) {
    tenderCommentService.saveBatch(tenderComments);
        return R.success();
        }
@PostMapping("/updateBatchById")
@ApiOperation(value = "根据主键进行批量修改")
public R updateBatchById(@RequestBody List<TenderComment> tenderComments) {
    tenderCommentService.updateBatchById(tenderComments );
        return R.success();
        }



}
