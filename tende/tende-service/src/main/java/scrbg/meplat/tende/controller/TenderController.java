package scrbg.meplat.tende.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.entity.Tender;
import scrbg.meplat.tende.service.TenderService;
import scrbg.meplat.tende.vo.TenderInfoVo;

/**
 * @描述：招标控制类
 * @作者: sund
 * @日期: 2023-02-03
 */
@RestController
@RequestMapping("/tender")
@Api(tags = "招标")
public class TenderController {

    @Autowired
    public TenderService tenderService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<Tender> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderService.LoginqueryPage(jsonObject, new LambdaQueryWrapper<Tender>());
        return PageR.success(page);
    }

    @PostMapping("/inspect/listByEntity")
    @ApiOperation(value = "纪检系统")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<Tender> inspectListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderService.inspectPage(jsonObject, new LambdaQueryWrapper<Tender>());
        return PageR.success(page);
    }

    @PostMapping("/attendBidList")
    @ApiOperation(value = "参与招标")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "unitCode", value = "社会信用代码", dataTypeClass = String.class ,required = true),
            @DynamicParameter(name = "mallConfig", value = "商城类型", dataTypeClass = Integer.class ,required = true)
    })
    public PageR<Tender> attendBidList(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderService.attendBidList(jsonObject, new QueryWrapper<Tender>());
        return PageR.success(page);
    }
    @PostMapping("/winBidList")
    @ApiOperation(value = "中标招标")
    @DynamicParameters(name = "中标招标", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "unitCode", value = "社会信用代码", dataTypeClass = String.class ,required = true),
            @DynamicParameter(name = "mallConfig", value = "商城类型", dataTypeClass = Integer.class ,required = true)
    })
    public PageR<Tender> winBidList(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderService.winBidList(jsonObject, new QueryWrapper<Tender>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Tender> findById(String id) {
        Tender tender = tenderService.getById(id);
        return R.success(tender);
    }


    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody Tender tender) {
        tenderService.create(tender);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody Tender tender) {
        tenderService.update(tender);
        return R.success();
    }

}

