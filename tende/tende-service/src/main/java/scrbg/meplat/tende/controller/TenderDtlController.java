package scrbg.meplat.tende.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.entity.TenderDtl;
import scrbg.meplat.tende.service.TenderDtlService;
import scrbg.meplat.tende.vo.TenderPackageVo;

import java.util.List;

/**
 * @描述：招标_清单明细控制类
 * @作者: sund
 * @日期: 2023-02-03
 */
@RestController
@RequestMapping("/tenderDtl")
@Api(tags = "招标_清单明细")
public class TenderDtlController {

    @Autowired
    public TenderDtlService tenderDtlService;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderDtl> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderDtlService.queryPage(jsonObject, new LambdaQueryWrapper<TenderDtl>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<TenderDtl> findById(String id) {
        TenderDtl tenderDtl = tenderDtlService.getById(id);
        return R.success(tenderDtl);
    }

    @PostMapping ("/getBathByPackageIds")
    @ApiOperation(value = "根据分包id查询")
    public R getBathByPackageIds(@RequestBody JSONObject jsonObject) {
        List<TenderPackageVo> tenderDtl = tenderDtlService.getBathByPackageIds(jsonObject);
        return R.success(tenderDtl);
    }

    @GetMapping("/getListByBillId")
    @ApiOperation(value = "根据招标申请号查询招标包件中招标清单信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R getListByBillId(String id) {

        List<TenderPackageVo>  list= tenderDtlService.getListByBillId(id);
        return R.success(list);
    }
    @GetMapping("/listByBillId")
    @ApiOperation(value = "根据招标id查询招标清单表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R listByBillId(String id) {
        List<TenderDtl> list = tenderDtlService.listByBillId(id);
        return R.success(list);
    }
    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody TenderDtl tenderDtl) {
        tenderDtlService.create(tenderDtl);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody TenderDtl tenderDtl) {
        tenderDtlService.update(tenderDtl);
        return R.success();
    }

}

