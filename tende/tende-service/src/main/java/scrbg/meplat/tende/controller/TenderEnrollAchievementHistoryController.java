package scrbg.meplat.tende.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.entity.TenderEnrollAchievementHistory;
import scrbg.meplat.tende.service.TenderEnrollAchievementHistoryService;

/**
 * @描述：招标_报名_企业主要业绩控制类
 * @作者: sund
 * @日期: 2023-02-03
 */
@RestController
@RequestMapping("/tenderEnrollAchievementHistory")
@Api(tags = "招标_报名_企业主要业绩")
public class TenderEnrollAchievementHistoryController {

    @Autowired
    public TenderEnrollAchievementHistoryService tenderEnrollAchievementHistoryService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderEnrollAchievementHistory> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderEnrollAchievementHistoryService.queryPage(jsonObject, new LambdaQueryWrapper<TenderEnrollAchievementHistory>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<TenderEnrollAchievementHistory> findById(String id) {
        TenderEnrollAchievementHistory tenderEnrollAchievementHistory = tenderEnrollAchievementHistoryService.getById(id);
        return R.success(tenderEnrollAchievementHistory);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody TenderEnrollAchievementHistory tenderEnrollAchievementHistory) {
        tenderEnrollAchievementHistoryService.create(tenderEnrollAchievementHistory);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody TenderEnrollAchievementHistory tenderEnrollAchievementHistory) {
        tenderEnrollAchievementHistoryService.update(tenderEnrollAchievementHistory);
        return R.success();
    }

}

