package scrbg.meplat.tende.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.tende.service.ITenderEnrollService;
import scrbg.meplat.tende.entity.TenderEnroll;


/**
 * @描述：物资供应招标_招标报名审核控制类
 * @作者: demo
 * @日期: 2023-02-20
 */
@RestController
@RequestMapping("/tenderEnroll")
@Api(tags = "物资供应招标_招标报名审核")
public class TenderEnrollController {

    @Autowired
    public ITenderEnrollService tenderEnrollService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = " 根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderEnroll> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderEnrollService.queryPage(jsonObject, new LambdaQueryWrapper());
        return PageR.success(page);
    }

    @PostMapping("unitCode/listByEntityByUnitCode")
    @ApiOperation(value = " 根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderEnroll> listByEntityByUnitCode(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderEnrollService.unitCodeListByEntity(jsonObject, new QueryWrapper());
        return PageR.success(page);
    }


    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<TenderEnroll> findById(String id) {
        TenderEnroll tenderEnroll = tenderEnrollService.getById(id);
        return R.success(tenderEnroll);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
    public R save(@RequestBody TenderEnroll tenderEnroll) {
        tenderEnrollService.create(tenderEnroll);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    @ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
    public R update(@RequestBody TenderEnroll tenderEnroll) {
        tenderEnrollService.update(tenderEnroll);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        tenderEnrollService.delete(id);
        return R.success();
    }
    @PostMapping("/addTenderApply")
    @ApiOperation(value = "招标包件报名")
    public R save(String id ){
        tenderEnrollService.addTenderApply(id);
        return R.success();
    }

}
