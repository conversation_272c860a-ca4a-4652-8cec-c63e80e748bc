package scrbg.meplat.tende.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.entity.TenderEnroll;
import scrbg.meplat.tende.entity.TenderEnrollPackage;
import scrbg.meplat.tende.entity.TenderPackage;
import scrbg.meplat.tende.service.TenderEnrollPackageService;

/**
 * @描述：招标_报名_合同段控制类
 * @作者: sund
 * @日期: 2023-02-03
 */
@RestController
@RequestMapping("/tenderEnrollPackage")
@Api(tags = "招标_报名_合同段")
public class TenderEnrollPackageController {

    @Autowired
    public TenderEnrollPackageService tenderEnrollPackageService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderEnrollPackage> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderEnrollPackageService.queryPage(jsonObject, new LambdaQueryWrapper<TenderEnrollPackage>());
        return PageR.success(page);
    }
    @PostMapping("/packageList")
    @ApiOperation(value = " 根据社会信用编码和招标id查询分包信息")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderPackage> list(@RequestBody JSONObject jsonObject){
        PageUtils page= tenderEnrollPackageService.Packageslist(jsonObject,new QueryWrapper());
        return PageR.success(page);
    }
    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<TenderEnrollPackage> findById(String id) {
        TenderEnrollPackage tenderEnrollPackage = tenderEnrollPackageService.getById(id);
        return R.success(tenderEnrollPackage);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody TenderEnrollPackage tenderEnrollPackage) {
        tenderEnrollPackageService.create(tenderEnrollPackage);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody TenderEnrollPackage tenderEnrollPackage) {
        tenderEnrollPackageService.update(tenderEnrollPackage);
        return R.success();
    }

}

