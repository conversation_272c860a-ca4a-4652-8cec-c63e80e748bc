package scrbg.meplat.tende.controller;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.tende.service.ITenderMessageService;
import scrbg.meplat.tende.entity.TenderMessage;

import java.util.List;


/**
 * @描述：招标消息表控制类
 * @作者: demo
 * @日期: 2023-02-21
 */
@RestController
@RequestMapping("/tenderMessage")
@Api(tags = "招标消息表")
public class TenderMessageController{

@Autowired
public ITenderMessageService tenderMessageService;

@PostMapping("/listByEntity")
@ApiOperation(value = " 根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<TenderMessage> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= tenderMessageService.queryPage(jsonObject, new LambdaQueryWrapper<TenderMessage>());
        return PageR.success(page);
        }

@GetMapping("/updateIsReadById")
@ApiOperation(value = "根据主键更改消息阅读状态")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R updateIsReadById(String id){
    tenderMessageService.updateIsReadById(id);
        return R.success();
        }
    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<TenderMessage> findById(String id){
        TenderMessage tenderMessage = tenderMessageService.getById(id);
        return R.success(tenderMessage);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    @ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
    public R save(@RequestBody TenderMessage tenderMessage){
        tenderMessageService.create(tenderMessage);
        return R.success();
    }
@PostMapping("/getMsgNumber")
@ApiOperation(value = "根据公司社会信用编码查询公司未读通知")
public R getMsgNumber(@RequestBody JSONObject jsonObject){
   List<TenderMessage> list =tenderMessageService.getMsgNumber(jsonObject);
        return R.success(list);
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
@ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
public R update(@RequestBody TenderMessage tenderMessage){
    tenderMessageService.update(tenderMessage);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    tenderMessageService.delete(id);
        return R.success();
        }
        }
