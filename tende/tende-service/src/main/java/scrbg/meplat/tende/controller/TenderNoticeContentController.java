package scrbg.meplat.tende.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.entity.TenderNoticeContent;
import scrbg.meplat.tende.service.TenderNoticeContentService;

/**
 * @描述：招标_中标通知书_通知内容控制类
 * @作者: sund
 * @日期: 2023-02-03
 */
@RestController
@RequestMapping("/tenderNoticeContent")
@Api(tags = "招标_中标通知书_通知内容")
public class TenderNoticeContentController {

    @Autowired
    public TenderNoticeContentService tenderNoticeContentService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderNoticeContent> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderNoticeContentService.queryPage(jsonObject, new LambdaQueryWrapper<TenderNoticeContent>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<TenderNoticeContent> findById(String id) {
        TenderNoticeContent tenderNoticeContent = tenderNoticeContentService.getById(id);
        return R.success(tenderNoticeContent);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody TenderNoticeContent tenderNoticeContent) {
        tenderNoticeContentService.create(tenderNoticeContent);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody TenderNoticeContent tenderNoticeContent) {
        tenderNoticeContentService.update(tenderNoticeContent);
        return R.success();
    }

}

