package scrbg.meplat.tende.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.entity.TenderNotice;
import scrbg.meplat.tende.service.TenderNoticeService;

/**
 * @描述：招标-中标通知书控制类
 * @作者: sund
 * @日期: 2023-02-03
 */
@RestController
@RequestMapping("/tenderNotice")
@Api(tags = "招标-中标通知书")
public class TenderNoticeController {

    @Autowired
    public TenderNoticeService tenderNoticeService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderNotice> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderNoticeService.queryPage(jsonObject, new LambdaQueryWrapper<TenderNotice>());
        return PageR.success(page);
    }
//    @PostMapping("/list")
//    @ApiOperation(value = "查询供应商中标信息")
//    @DynamicParameters(name = "根据实体属性分页查询", properties = {
//            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
//            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
//    })
//    public PageR<TenderNotice> listByUn(@RequestBody JSONObject jsonObject) {
//        PageUtils page = tenderNoticeService.queryPageByUnitCode(jsonObject, new LambdaQueryWrapper<TenderNotice>());
//        return PageR.success(page);
//    }
    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<TenderNotice> findById(String id) {
        TenderNotice tenderNotice = tenderNoticeService.getById(id);
        return R.success(tenderNotice);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody TenderNotice tenderNotice) {
        tenderNoticeService.create(tenderNotice);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody TenderNotice tenderNotice) {
        tenderNoticeService.update(tenderNotice);
        return R.success();
    }

}

