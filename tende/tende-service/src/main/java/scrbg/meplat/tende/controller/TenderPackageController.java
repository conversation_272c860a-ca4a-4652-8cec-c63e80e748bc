package scrbg.meplat.tende.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.vo.TenderPackageInfoVo;
import scrbg.meplat.tende.vo.TenderPackageVo;
import scrbg.meplat.tende.entity.TenderPackage;
import scrbg.meplat.tende.service.TenderPackageService;

import java.util.List;

/**
 * @描述：招标-包件控制类
 * @作者: sund
 * @日期: 2023-02-03
 */
@RestController
@RequestMapping("/tenderPackage")
@Api(tags = "招标-包件")
public class TenderPackageController {

    @Autowired
    public TenderPackageService tenderPackageService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderPackage> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderPackageService.queryPage(jsonObject, new LambdaQueryWrapper<TenderPackage>());
        return PageR.success(page);
    }
    @PostMapping("/winBidPackageList")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderPackage> winBidList(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderPackageService.winBidPackageList(jsonObject, new QueryWrapper<TenderPackage>());
        return PageR.success(page);
    }
    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<TenderPackage> findById(String id) {
        TenderPackage tenderPackage = tenderPackageService.getById(id);
        return R.success(tenderPackage);
    }

    @GetMapping("/findTenderDtlsByBillIdAll")
    @ApiOperation(value = "根据招标id查询招标中包件的清单信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "billId", required = true,
                    dataType = "String", paramType = "query")
    })
    public R findByBillIdAll(String billId) {
       List<TenderPackageVo> tenderPackage = tenderPackageService.findByBillIdAll(billId);
        return R.success(tenderPackage);
    }
    @GetMapping("/listByBillId")
    @ApiOperation(value = "根据招标申请号查询招标包件信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R listByBillId(String id) {

        List<TenderPackage>  list= tenderPackageService.listByBillId(id);
        return R.success(list);
    }
    @GetMapping("/TeBerSuByBillId")
    @ApiOperation(value = "根据招标申请号查询招标包件中参与公司信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R TeBerSuByBillId(String id) {

        List<TenderPackageVo>  list= tenderPackageService.TeBerSuByBillId(id);
        return R.success(list);
    }
    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody TenderPackage tenderPackage) {
        tenderPackageService.create(tenderPackage);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody TenderPackage tenderPackage) {
        tenderPackageService.update(tenderPackage);
        return R.success();
    }

}

