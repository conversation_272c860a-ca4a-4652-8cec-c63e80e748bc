package scrbg.meplat.tende.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.entity.TenderPackageSubcontractor;
import scrbg.meplat.tende.service.TenderPackageSubcontractorService;

/**
 * @描述：招标-包件分包商控制类
 * @作者: sund
 * @日期: 2023-02-03
 */
@RestController
@RequestMapping("/tenderPackageSubcontractor")
@Api(tags = "招标-包件分包商")
public class TenderPackageSubcontractorController {

    @Autowired
    public TenderPackageSubcontractorService tenderPackageSubcontractorService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderPackageSubcontractor> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderPackageSubcontractorService.queryPage(jsonObject, new LambdaQueryWrapper<TenderPackageSubcontractor>());
        return PageR.success(page);
    }


    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<TenderPackageSubcontractor> findById(String id) {
        TenderPackageSubcontractor tenderPackageSubcontractor = tenderPackageSubcontractorService.getById(id);
        return R.success(tenderPackageSubcontractor);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody TenderPackageSubcontractor tenderPackageSubcontractor) {
        tenderPackageSubcontractorService.create(tenderPackageSubcontractor);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody TenderPackageSubcontractor tenderPackageSubcontractor) {
        tenderPackageSubcontractorService.update(tenderPackageSubcontractor);
        return R.success();
    }

}

