package scrbg.meplat.tende.controller;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.tende.service.ITenderPublicityCandidateService;
import scrbg.meplat.tende.entity.TenderPublicityCandidate;


/**
 * @描述：物资供应招标_公示_候选人控制类
 * @作者: demo
 * @日期: 2023-02-15
 */
@RestController
@RequestMapping("/tenderPublicityCandidate")
@Api(tags = "物资供应招标_公示_候选人")
public class TenderPublicityCandidateController{

@Autowired
public ITenderPublicityCandidateService tenderPublicityCandidateService;

@PostMapping("/listByEntity")
@ApiOperation(value = " 根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<TenderPublicityCandidate> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= tenderPublicityCandidateService.queryPage(jsonObject, new LambdaQueryWrapper());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<TenderPublicityCandidate> findById(String id){
    TenderPublicityCandidate tenderPublicityCandidate = tenderPublicityCandidateService.getById(id);
        return R.success(tenderPublicityCandidate);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
@ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
public R save(@RequestBody TenderPublicityCandidate tenderPublicityCandidate){
    tenderPublicityCandidateService.create(tenderPublicityCandidate);
        return R.success();
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
@ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
public R update(@RequestBody TenderPublicityCandidate tenderPublicityCandidate){
    tenderPublicityCandidateService.update(tenderPublicityCandidate);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    tenderPublicityCandidateService.delete(id);
        return R.success();
        }
        }
