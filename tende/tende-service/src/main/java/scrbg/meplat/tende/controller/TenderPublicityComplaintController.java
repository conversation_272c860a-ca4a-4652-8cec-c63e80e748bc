package scrbg.meplat.tende.controller;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.tende.service.ITenderPublicityComplaintService;
import scrbg.meplat.tende.entity.TenderPublicityComplaint;


/**
 * @描述：物资供应招标_招标结果公示_投诉受理控制类
 * @作者: demo
 * @日期: 2023-02-15
 */
@RestController
@RequestMapping("/tenderPublicityComplaint")
@Api(tags = "物资供应招标_招标结果公示_投诉受理")
public class TenderPublicityComplaintController{

@Autowired
public ITenderPublicityComplaintService tenderPublicityComplaintService;

@PostMapping("/listByEntity")
@ApiOperation(value = " 根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<TenderPublicityComplaint> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= tenderPublicityComplaintService.queryPage(jsonObject,new LambdaQueryWrapper<TenderPublicityComplaint>());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<TenderPublicityComplaint> findById(String id){
    TenderPublicityComplaint tenderPublicityComplaint = tenderPublicityComplaintService.getById(id);
        return R.success(tenderPublicityComplaint);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
@ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
public R save(@RequestBody TenderPublicityComplaint tenderPublicityComplaint){
    tenderPublicityComplaintService.create(tenderPublicityComplaint);
        return R.success();
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
@ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
public R update(@RequestBody TenderPublicityComplaint tenderPublicityComplaint){
    tenderPublicityComplaintService.update(tenderPublicityComplaint);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    tenderPublicityComplaintService.delete(id);
        return R.success();
        }
        }
