package scrbg.meplat.tende.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.entity.TenderResult;
import scrbg.meplat.tende.service.TenderResultService;

/**
 * @描述：招标_评标结果登记控制类
 * @作者: sund
 * @日期: 2023-02-03
 */
@RestController
@RequestMapping("/tenderResult")
@Api(tags = "招标_评标结果登记")
public class TenderResultController {

    @Autowired
    public TenderResultService tenderResultService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderResult> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderResultService.queryPage(jsonObject, new LambdaQueryWrapper<TenderResult>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<TenderResult> findById(String id) {
        TenderResult tenderResult = tenderResultService.getById(id);
        return R.success(tenderResult);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody TenderResult tenderResult) {
        tenderResultService.create(tenderResult);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody TenderResult tenderResult) {
        tenderResultService.update(tenderResult);
        return R.success();
    }

}

