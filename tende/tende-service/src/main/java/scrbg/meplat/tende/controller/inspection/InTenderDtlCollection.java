package scrbg.meplat.tende.controller.inspection;

import com.alibaba.fastjson.JSONObject;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.tende.service.TenderDtlService;
import scrbg.meplat.tende.vo.TenderPackageVo;

import java.util.List;

@RestController
@RequestMapping("/inspection/tenderDtl")
@Api(tags = "纪检系统-招标清单信息")
public class InTenderDtlCollection {

    @Autowired
    public TenderDtlService tenderDtlService;

    @PostMapping("/getBathByPackageIds")
    @ApiOperation(value = "根据分包id查询")
    public R getBathByPackageIds(@RequestBody JSONObject jsonObject) {
        List<TenderPackageVo> tenderDtl = tenderDtlService.getBathByPackageIds(jsonObject);
        return R.success(tenderDtl);
    }


}
