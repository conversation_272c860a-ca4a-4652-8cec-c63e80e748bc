package scrbg.meplat.tende.controller.inspection;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.entity.TenderPackage;
import scrbg.meplat.tende.service.TenderPackageService;
import scrbg.meplat.tende.vo.TenderPackageVo;

import java.util.List;

@RestController
@RequestMapping("/inspection/tenderPackage")
@Api(tags = "纪检系统-招标包件信息")
public class InTenderPackageController {

    @Autowired
    public TenderPackageService tenderPackageService;

    @GetMapping("/TeBerSuByBillId")
    @ApiOperation(value = "根据招标申请号查询招标包件中参与公司信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R TeBerSuByBillId(String id) {

        List<TenderPackageVo> list= tenderPackageService.TeBerSuByBillId(id);
        return R.success(list);
    }

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderPackage> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderPackageService.queryPage(jsonObject, new LambdaQueryWrapper<TenderPackage>());
        return PageR.success(page);
    }



}
