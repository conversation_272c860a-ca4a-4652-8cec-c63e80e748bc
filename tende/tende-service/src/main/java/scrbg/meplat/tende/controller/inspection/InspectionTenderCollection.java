package scrbg.meplat.tende.controller.inspection;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.tende.entity.Tender;
import scrbg.meplat.tende.service.TenderService;

@RestController
@RequestMapping("/inspection/tender")
@Api(tags = "纪检系统-招标")
public class InspectionTenderCollection {
    @Autowired
    public TenderService tenderService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "纪检系统")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "mallType", value = "商城类型", dataTypeClass = Integer.class)
    })
    public PageR<Tender> inspectListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderService.inspectPage(jsonObject, new LambdaQueryWrapper<Tender>());
        return PageR.success(page);
    }

}
