package scrbg.meplat.tende.controller.supplierSys;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.entity.Tender;
import scrbg.meplat.tende.service.TenderDtlService;
import scrbg.meplat.tende.service.TenderEnrollPackageService;
import scrbg.meplat.tende.service.TenderPackageService;
import scrbg.meplat.tende.service.TenderService;
import scrbg.meplat.tende.vo.TenderPackageVo;

import java.util.List;

@RestController
@RequestMapping("/supplierSys")
@Api(tags = "供应商系统-投标管理")

public class BidTender {

    @Autowired
    public TenderService tenderService;
    @Autowired
    public TenderPackageService tenderPackageService;

    @Autowired
    public TenderEnrollPackageService tenderEnrollPackageService;
    @Autowired
    public TenderDtlService tenderDtlService;

    @PostMapping("/tender/winBidList")
    @ApiOperation(value = "中标招标")
    @DynamicParameters(name = "中标招标", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "mallConfig", value = "商城类型", dataTypeClass = Integer.class ,required = true)
    })
    public PageR<Tender> winBidList(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderService.winBidList(jsonObject, new QueryWrapper<Tender>());
        return PageR.success(page);
    }


    @GetMapping("/tender/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Tender> findById(String id) {
        Tender tender = tenderService.getById(id);
        return R.success(tender);
    }

    @PostMapping("/tender/attendBidList")
    @ApiOperation(value = "参与招标")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "mallConfig", value = "商城类型", dataTypeClass = Integer.class ,required = true)
    })
    public PageR<Tender> attendBidList(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderService.attendBidList(jsonObject, new QueryWrapper<Tender>());
        return PageR.success(page);
    }






    @PostMapping ("/tenderDtl/getBathByPackageIds")
    @ApiOperation(value = "根据分包id查询")
    public R getBathByPackageIds(@RequestBody JSONObject jsonObject) {
        List<TenderPackageVo> tenderDtl = tenderDtlService.getBathByPackageIds(jsonObject);
        return R.success(tenderDtl);
    }

}

