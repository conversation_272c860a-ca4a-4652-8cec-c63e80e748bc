package scrbg.meplat.tende.controller.supplierSys;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import scrbg.meplat.tende.entity.TenderEnrollPackage;
import scrbg.meplat.tende.entity.TenderPackage;
import scrbg.meplat.tende.service.TenderEnrollPackageService;

@RestController
@RequestMapping("/supplierSys/tenderEnrollPackage")
@Api(tags = "供应商系统-报名信息")
public class STenderEnrollPackages {
    @Autowired
    public TenderEnrollPackageService tenderEnrollPackageService;


    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderEnrollPackage> tenderEnrollPackageListByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderEnrollPackageService.queryPage(jsonObject, new LambdaQueryWrapper<TenderEnrollPackage>());
        return PageR.success(page);
    }


    @PostMapping("/packageList")
    @ApiOperation(value = " 根据社会信用编码和招标id查询分包信息")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderPackage> list(@RequestBody JSONObject jsonObject){
        PageUtils page= tenderEnrollPackageService.Packageslist(jsonObject,new QueryWrapper());
        return PageR.success(page);
    }




}
