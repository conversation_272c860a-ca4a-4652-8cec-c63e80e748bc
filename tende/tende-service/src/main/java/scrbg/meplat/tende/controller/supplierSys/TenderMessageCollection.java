package scrbg.meplat.tende.controller.supplierSys;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.tende.entity.TenderMessage;
import scrbg.meplat.tende.service.ITenderMessageService;

import java.util.List;

@RestController
@RequestMapping("/supplierSys/tenderMessage")
@Api(tags = "供应商系统-我的消息")
public class TenderMessageCollection {

    @Autowired
    public ITenderMessageService tenderMessageService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = " 根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "mallType", value = "商城类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<TenderMessage> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= tenderMessageService.queryPage(jsonObject, new LambdaQueryWrapper<TenderMessage>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<TenderMessage> findById(String id){
        TenderMessage tenderMessage = tenderMessageService.getById(id);
        return R.success(tenderMessage);
    }


    @GetMapping("/updateIsReadById")
    @ApiOperation(value = "根据主键更改消息阅读状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R updateIsReadById(String id){
        tenderMessageService.updateIsReadById(id);
        return R.success();
    }


    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id){
        tenderMessageService.delete(id);
        return R.success();
    }

    @PostMapping("/getMsgNumber")
    @ApiOperation(value = "根据公司社会信用编码查询公司未读通知")
    public R getMsgNumber(@RequestBody JSONObject jsonObject){
        List<TenderMessage> list =tenderMessageService.getMsgNumber(jsonObject);
        return R.success(list);
    }

}
