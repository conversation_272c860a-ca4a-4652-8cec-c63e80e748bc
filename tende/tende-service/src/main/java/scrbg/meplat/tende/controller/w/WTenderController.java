package scrbg.meplat.tende.controller.w;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;


import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;

import scrbg.meplat.tende.entity.Tender;
import scrbg.meplat.tende.service.TenderService;
import scrbg.meplat.tende.vo.TenderResultVo;

import java.awt.geom.Point2D;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;


/**
 * @描述：招标控制类
 * @作者: demo
 * @日期: 2023-02-10
 */
@RestController
@RequestMapping("/w/tender")
@Api(tags = "未登时招标")
public class WTenderController {
    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    public TenderService tenderService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = " 根据实体属性分页查询（未登时招标公开招标）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "mallConfig", value = "商场类型 （0 物资 1装备）", dataTypeClass = Integer.class)
    })
    public PageR<Tender> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = tenderService.queryPage(jsonObject, new LambdaQueryWrapper<Tender>());
        return PageR.success(page);
    }

    @PostMapping("/getTenderNum")
    @ApiOperation(value = " 查询一段时间招标总数")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "startDate", value = "开始时间"),
            @DynamicParameter(name = "endDate", value = "结束时间")
    })
    public R getTenderNum(@RequestBody JSONObject jsonObject) {
        TenderResultVo tenderNum = tenderService.getTenderNum(jsonObject, new LambdaQueryWrapper<Tender>());
        return R.success(tenderNum);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<Tender> findById(String id) {
        Tender tender = tenderService.getById(id);
        return R.success(tender);
    }


    @PostMapping("/saveBatch")
    @ApiOperation(value = "批量添加")
    public R saveBatch(@RequestBody List<Tender> tenders) {
        tenderService.saveBatch(tenders);
        return R.success();
    }

    @PostMapping("/updateBatchById")
    @ApiOperation(value = "根据主键进行批量修改")
    public R updateBatchById(@RequestBody List<Tender> tenders) {
        tenderService.updateBatchById(tenders);
        return R.success();
    }

    @GetMapping("/text")
    @ApiOperation(value = "rrrrrrr")
    public void text() {

        BufferedWriter bw = null;
        try {
            File file = new File("/home/<USER>/points.txt");
            if (!file.exists()) {
                file.createNewFile();
            }
            FileWriter fw = new FileWriter(file.getAbsoluteFile());
            bw = new BufferedWriter(fw);


                String s ="333";
                try {
                    bw.write(s);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                bw.write(' ');
                bw.write(s);


            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
