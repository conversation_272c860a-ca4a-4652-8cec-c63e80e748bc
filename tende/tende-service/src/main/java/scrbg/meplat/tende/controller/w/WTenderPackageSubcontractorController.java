package scrbg.meplat.tende.controller.w;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.tende.entity.TenderPackageSubcontractor;
import scrbg.meplat.tende.service.TenderPackageSubcontractorService;

import java.util.List;


/**
 * @描述：招标-包件分包商控制类
 * @作者: demo
 * @日期: 2023-02-10
 */
@RestController
@RequestMapping("/w/tenderPackageSubcontractor")
@Api(tags = "招标-包件分包商")
public class WTenderPackageSubcontractorController{

@Autowired
public TenderPackageSubcontractorService tenderPackageSubcontractorService;

@PostMapping("/listByEntity")
@ApiOperation(value = " 根据实体属性分页查询")
@DynamicParameters(name = "根据实体属性分页查询", properties = {
        @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
        @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
})
public PageR<TenderPackageSubcontractor> listByEntity(@RequestBody JSONObject jsonObject){
        PageUtils page= tenderPackageSubcontractorService.queryPage(jsonObject,new LambdaQueryWrapper<TenderPackageSubcontractor>());
        return PageR.success(page);
        }

@GetMapping("/findById")
@ApiOperation(value = "根据主键查询")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R<TenderPackageSubcontractor> findById(String id){
    TenderPackageSubcontractor tenderPackageSubcontractor = tenderPackageSubcontractorService.getById(id);
        return R.success(tenderPackageSubcontractor);
        }

@PostMapping("/create")
@ApiOperation(value = "新增")
@ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
public R save(@RequestBody TenderPackageSubcontractor tenderPackageSubcontractor){
    tenderPackageSubcontractorService.create(tenderPackageSubcontractor);
        return R.success();
        }

@PostMapping("/update")
@ApiOperation(value = "修改")
@ApiOperationSupport(ignoreParameters = {"接口文档忽略字段1", "接口文档忽略字段2"})
public R update(@RequestBody TenderPackageSubcontractor tenderPackageSubcontractor){
    tenderPackageSubcontractorService.update(tenderPackageSubcontractor);
        return R.success();
        }

@GetMapping("/delete")
@ApiOperation(value = "根据主键删除")
@ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "ID", required = true,
                dataType = "String", paramType = "query")
})
public R delete(String id){
    tenderPackageSubcontractorService.delete(id);
        return R.success();
        }

@PostMapping("/saveBatch")
@ApiOperation(value = "批量添加")
public R saveBatch(@RequestBody List<TenderPackageSubcontractor> tenderPackageSubcontractors) {
    tenderPackageSubcontractorService.saveBatch(tenderPackageSubcontractors);
        return R.success();
        }
@PostMapping("/updateBatchById")
@ApiOperation(value = "根据主键进行批量修改")
public R updateBatchById(@RequestBody List<TenderPackageSubcontractor> tenderPackageSubcontractors) {
    tenderPackageSubcontractorService.updateBatchById(tenderPackageSubcontractors );
        return R.success();
        }



}
