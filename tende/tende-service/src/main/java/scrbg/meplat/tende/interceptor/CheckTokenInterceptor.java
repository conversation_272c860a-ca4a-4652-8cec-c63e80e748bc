package scrbg.meplat.tende.interceptor;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scrbg.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.HandlerInterceptor;
import scrbg.meplat.tende.common.redis.RedisKey;
import scrbg.meplat.tende.config.MallConfig;
import scrbg.meplat.tende.exception.BusinessException;
import scrbg.meplat.tende.util.ThreadLocalUtil;
import scrbg.meplat.tende.util.UserLogin;
import scrbg.meplat.tende.vo.user.LoginVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2022-12-14 16:39
 */
@Component
public class CheckTokenInterceptor implements HandlerInterceptor {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private MallConfig mallConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 放行options请求
        String method = request.getMethod();
        if ("OPTIONS".equalsIgnoreCase(method)) {
            return true;
        }
        String requestURI = request.getRequestURI();

//        System.out.println("请求---------" + request.getRequestURI());
        String token = request.getHeader("token");
        Integer mallType = Integer.valueOf(request.getHeader("mallType"));
//        System.out.println("token---------------" + token);
        if (token == null) {
            throw new BusinessException(400, "请先登陆");
        } else {
            UserLogin user = new UserLogin();
            String url = mallConfig.prodPcwp2Url + "/identity/auth/verifyToken?token=" + token;
            HttpHeaders thisHeaders =  new HttpHeaders();
            if(mallType == 1) {
                thisHeaders.add("sysCode", "egp");
            }else {
                thisHeaders.add("sysCode", "msp");
            }
            HashMap<String, Object> thisMap = new HashMap<>();
            if(mallType == 1) {
                thisMap.put("sysCode", "egp");
            }else {
                thisMap.put("sysCode", "msp");
            }
            String content = JSON.toJSONString(thisMap);
            HttpEntity<String> thisRequest = new HttpEntity<>(content,thisHeaders);
            R<Map> r = restTemplate.postForObject(url, thisRequest, R.class);
//                System.out.println("校验token返回：" + r);
            if (r.getCode() == 200) {
                Map dataMap = r.getData();
                String id = (String) dataMap.get("userId");
                if (stringRedisTemplate.hasKey(RedisKey.USER_MAP_KEY + mallType + "_" + id)) {
                    LoginVO vo = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.USER_MAP_KEY + mallType + "_" + id), LoginVO.class);
                    user.setUserId(vo.getUserId());
                    user.setShopId(vo.getShopId());
                    user.setOrgId(vo.getOrgId());
                    user.setEnterpriseId(vo.getLocalOrgId());
                    user.setFarUserId(vo.getFarUserId());
                    user.setOrgIds(vo.getOrgIds());
                    user.setUserMobile(vo.getUserMobile());
                    user.setToken(vo.getToken());
                    user.setIsInterior(vo.getIsInterior());
                    user.setEnterpriseName(vo.getEnterpriseName());
                    user.setUserName(vo.getUserName());
                    user.setOrgInfo(vo.getOrgInfo());
                    user.setSocialCreditCode(vo.getSocialCreditCode());
                    user.setIsCheck(vo.getIsCheck());
                    user.setIsSupplier(vo.getIsSupplier());
                    ThreadLocalUtil.addCurrentUser(user);
                    stringRedisTemplate.expire(RedisKey.USER_MAP_KEY + mallType + "_" + id,mallConfig.loginOutTime, TimeUnit.MINUTES);
                    if (requestURI.contains("/inspection")){
                        user = ThreadLocalUtil.getCurrentUser();
                        if (user.getIsCheck()!=1){
                            throw new BusinessException(400, "没有纪检权限，拒绝访问");
                        }
                    }
                    else  if(requestURI.contains("/supplierSys")){
                        if (user.getIsSupplier()!=1){
                            throw new BusinessException(400, "没有供应商权限，拒绝访问");
                        }
                    }else {
//                        if (user.getIsTender()!=1){
//                            throw new BusinessException(4010, "你没有招标权限，无法查看信息");
//                        }
                    }
                    return true;
                }
                else {
                    throw new BusinessException(4010, "登录过期，请重新登陆");
                }

            } else {
                throw new BusinessException(r.getCode(), r.getMessage());
            }

        }
    }

    private void doResponse(HttpServletResponse response, R r) throws IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        PrintWriter out = response.getWriter();
        String s = new ObjectMapper().writeValueAsString(r);
        out.print(s);
        out.flush();
        out.close();
    }

    /**
     * 接口访问结束后，从ThreadLocal中删除用户信息
     *
     * @param response
     * @param handler
     * @param ex
     * @throws Exception
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception
            ex) throws Exception {
        ThreadLocalUtil.remove();
    }
}
