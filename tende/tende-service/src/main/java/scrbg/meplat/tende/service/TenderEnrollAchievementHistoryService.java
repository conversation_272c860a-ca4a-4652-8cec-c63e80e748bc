package scrbg.meplat.tende.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.tende.entity.TenderEnrollAchievementHistory;

/**
 * @描述：招标_报名_企业主要业绩 服务类
 * @作者: sund
 * @日期: 2023-02-03
 */
public interface TenderEnrollAchievementHistoryService extends IService<TenderEnrollAchievementHistory> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<TenderEnrollAchievementHistory> queryWrapper);

        void create(TenderEnrollAchievementHistory tenderEnrollAchievementHistory);

        void update(TenderEnrollAchievementHistory tenderEnrollAchievementHistory);

        TenderEnrollAchievementHistory getById(String id);

        void delete(String id);
}
