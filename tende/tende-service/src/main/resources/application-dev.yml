#Seata配置
seata:
  enabled: false

spring:
  redis:
    host: *************
    port: 6389
    password: qazxsw!@121edc
#    host: ************
#    port: 6379
#    password: 2l@so8T#19Dp
  rabbitmq:
    host: *************
    port: 5672
    virtual-host: vhost_mall
    username: admin
    password: ystex@12st
  cache:
    type: redis # 使用redis作为缓存
    redis:
       #      time-to-live: 3600s # 过期时间
       # key-prefix: CACHE_ # 会导致自己在@Cacheable里设置的名字失效，
       use-key-prefix: true # key值加前缀，不声明默认是使用区域作为前缀
       cache-null-values: true # 缓存控制
  #数据源配置
  datasource:
    username: root
    password: qlows@12js
    url: jdbc:mysql://************:3306/tender-dev?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&useSSL=false&rewriteBatchedStatements=true
#    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    driver-class-name: com.mysql.cj.jdbc.Driver
  #云配置
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        #ip: *************
        register-enabled: true
mall:
  loginOutTime: 20
  #  prodPcwp2Url: "http://pcwp2-api.scrbg.com"
  prodPcwp2Url: "http://**************:15101"
  mallDeviceUrl: "http://************:9010"
  mallMarialUrl: "http://************:9011"
#日志配置
logging:
  level:
    srig.dcmp.base: error
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

host:
  deviceIp: "http://************:9011"
  materialIp: "http://************:9010"
