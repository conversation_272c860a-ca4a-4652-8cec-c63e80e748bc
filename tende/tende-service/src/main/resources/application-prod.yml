#Seata配置
seata:
  enabled: false

spring:
  redis:
    host: ************
    port: 6380
    password: tl@o23T#19N#
  cache:
    type: redis # 使用redis作为缓存
    redis:
      #      time-to-live: 3600s # 过期时间
      # key-prefix: CACHE_ # 会导致自己在@Cacheable里设置的名字失效，
      use-key-prefix: true # key值加前缀，不声明默认是使用区域作为前缀
      cache-null-values: true # 缓存控制
  rabbitmq:
    host: ************ # ip
    port: 5673
    username: mall_user
    password: lsjfla@1902s
    virtual-host: vhost_mall
  #数据源配置
  datasource:
    username: root
    password: qwert@1234aa%.*
    url: *****************************************************************************************************************************************************
    #    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      stat-view-servlet:
        enabled: false
  #云配置
  cloud:
    nacos:
      discovery:
        server-addr: ************:9848
        username: nacos # Nacos 用户名
        password: SL@sl1#lT9@19 # Nacos 密码
        register-enabled: true
        ip: ************
#日志配置
logging:
  level:
    srig.dcmp.base: error
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
mall:
  loginOutTime: 20
  prodPcwp2Url: "http://pcwp2-api.scrbg.com"
  mallDeviceUrl: "http://*************:9010"
  mallMarialUrl: "http://*************:9011"
#  prodPcwp2Url: "http://***************:6001"

host:
  deviceIp: "http://************:9011"
  materialIp: "http://************:9010"
knife4j:
  production: true
