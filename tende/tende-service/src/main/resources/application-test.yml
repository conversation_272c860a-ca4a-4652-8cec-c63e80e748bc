seata:
  enabled: true
  application-id: demo
  tx-service-group: my_test_tx_group
  enable-auto-data-source-proxy: true
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: ***************:8848
      group: SEATA_GROUP
      cluster: default
  config:
    type: nacos
    nacos:
      server-addr: ***************:8848
      group: SEATA_GROUP
      namespace: eeba1f43-adcf-449f-bc82-754550cce91b
  service:
    vgroup-mapping:
      my_test_tx_group: default
    disable-global-transaction: false
  client:
    rm:
      report-success-enable: false
      lock:
        retry-interval: 20 #校验或占用全局锁重试间隔 默认10,单位毫秒
        retry-times: 60 #校验或占用全局锁重试次数 默认30

spring:
  #数据源配置
  datasource:
    username: root
#    password: 123
#    url: ***********************************************************************************************************************************************
    password: asdw@123
    url: ***********************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
  #云配置
  cloud:
    nacos:
      discovery:
        register-enabled: true
        server-addr: ***************:8848
