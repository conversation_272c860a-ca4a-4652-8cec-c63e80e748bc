server:
  port: 9030
  tomcat:
    uri-encoding: UTF-8
    max-swallow-size: -1 #内置tomcat限制文件最大大小 -1为不限制


spring:
  #管理文件上传和请求数据大小
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB  #单文件最大限制
      max-request-size: 10MB #总文件最大限制
      file-size-threshold: 10MB
  main:
    allow-bean-definition-overriding: true
  application:
    name: mall-tender
  #本地环境
  profiles:
    active: "prod"
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
#接口文档
knife4j:
  enable: true
  setting:
    enableSwaggerModels: true
    swaggerModelName: 实体类列表

#mybatisPlus配置
mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: auto
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #打印SQL

#fegin调用超时时常
ribbon:
  OkToRetryOnAllOperations: false #对所有操作请求都进行重试,默认false
  ReadTimeout: 30000   #负载均衡超时时间，默认值5000
  ConnectTimeout: 30000 #ribbon请求连接的超时时间，默认值2000
  MaxAutoRetries: 0     #对当前实例的重试次数，默认0
  MaxAutoRetriesNextServer: 1 #对切换实例的重试次数，默认1

pcwp:
  #MinIO的配置，后面放到数据库中配置
  minIO:
    accessKey: AKIAIOSFODNN7EXAMPLE
    secretKey: wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
    Url:
      host: http://**************
      port: 9090
http:
   device:
     url: 127.0.0.1
     port: 9011
   material:
     url: 127.0.0.1
     port: 9010

mall:
  loginOutTime: 20
  prodPcwp2Url: "http://pcwp2-api.scrbg.com"
#  prodPcwp2Url: "http://***************:6001"
#  正式rabbitMq
tender:
  mallTender: mall-tender
  mallChang:  mall-chang
  mallNotice: tender-notice
  mallResult:  mall-result
  mallPublicity: mall-publicity
  mallEnroll: tender-enroll
#//测试
#tender:
#  mallTender: test-tender
#  mallChang: test-chang
#  mallNotice: test-notice
#  mallResult: test-result
#  mallPublicity: test-publicity
#  mallEnroll: test-enroll
