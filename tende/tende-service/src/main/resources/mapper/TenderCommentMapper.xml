<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.tende.mapper.TenderCommentMapper">
    <resultMap id="BaseResultMap" type="scrbg.meplat.tende.entity.TenderComment">
        <id property="billId" column="bill_id" jdbcType="CHAR"/>
        <id property="commentId" column="comment_id" jdbcType="TIMESTAMP"/>
        <result property="commentNo" column="comment_no" jdbcType="VARCHAR"/>
        <result property="billId" column="bill_id" jdbcType="CHAR"/>
        <result property="packageId" column="package_id" jdbcType="CHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="accessory" column="accessory" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="DATE"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="DATE"/>
    </resultMap>
</mapper>
