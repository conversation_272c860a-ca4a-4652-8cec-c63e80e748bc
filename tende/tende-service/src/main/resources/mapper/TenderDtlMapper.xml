<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.tende.mapper.TenderDtlMapper">

    <resultMap id="BaseResultMap" type="scrbg.meplat.tende.entity.TenderDtl">
            <id property="recordId" column="record_id" jdbcType="CHAR"/>
            <result property="billId" column="bill_id" jdbcType="CHAR"/>
            <result property="packageId" column="package_id" jdbcType="CHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="unit" column="unit" jdbcType="VARCHAR"/>
            <result property="texture" column="texture" jdbcType="VARCHAR"/>
            <result property="specs" column="specs" jdbcType="VARCHAR"/>
            <result property="num" column="num" jdbcType="DECIMAL"/>
            <result property="limitPrice" column="limit_price" jdbcType="DECIMAL"/>
            <result property="freightPrice" column="freight_price" jdbcType="DECIMAL"/>
            <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
            <result property="amount" column="amount" jdbcType="DECIMAL"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="estimatedStartTime" column="estimated_start_time" jdbcType="DATE"/>
            <result property="estimatedEndTime" column="estimated_end_time" jdbcType="DATE"/>
            <result property="estimatedServiceTime" column="estimated_service_time" jdbcType="INTEGER"/>
            <result property="materialTypeId" column="material_type_id" jdbcType="CHAR"/>
            <result property="materialNameId" column="material_name_id" jdbcType="CHAR"/>
            <result property="materialId" column="material_id" jdbcType="CHAR"/>
            <result property="baseLibraryId" column="base_library_id" jdbcType="CHAR"/>
            <result property="dataType" column="data_type" jdbcType="INTEGER"/>
            <result property="timingUnit" column="timing_unit" jdbcType="VARCHAR"/>
            <result property="changeReason" column="change_Reason" jdbcType="VARCHAR"/>
            <result property="freightTax" column="freight_tax" jdbcType="DECIMAL"/>
            <result property="recordType" column="record_type" jdbcType="TINYINT"/>
            <result property="scrapApplicationNo" column="scrap_application_no" jdbcType="VARCHAR"/>
            <result property="sisposalMethod" column="sisposal_method" jdbcType="VARCHAR"/>
            <result property="disposalAmount" column="disposal_amount" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        record_id,bill_id,package_id,
        name,type,unit,
        specs,num,limit_price,
        freight_price,unit_price,amount,
        gmt_create,gmt_modified,estimated_start_time,
        estimated_end_time,estimated_service_time,material_type_id,
        material_name_id,material_id,base_library_id,
        data_type,timing_unit,freight_tax,
        record_type,scrap_application_no,sisposal_method,
        disposal_amount,texture
    </sql>
    <resultMap id="tenderMap" type="scrbg.meplat.tende.vo.TenderPackageVo">
        <id property="dtl_id" column="dtl_id" jdbcType="CHAR"/>
        <result property="billId" column="bill_id" jdbcType="CHAR"/>
        <result property="itemNo" column="pitemNo" jdbcType="VARCHAR"/>
        <result property="name" column="pname" jdbcType="VARCHAR"/>
        <collection property="list" javaType="list" ofType="scrbg.meplat.tende.entity.TenderDtl">
            <result property="packageId" column="package_id" />
            <result property="billId" column="bill_id" />
            <result property="name" column="name" />
            <result property="type" column="type" />
            <result property="unit" column="unit" />
            <result property="specs" column="specs" />
            <result property="limitPrice" column="limit_price" />
            <result property="freightPrice" column="freight_price" />
            <result property="unitPrice" column="unit_price" />
            <result property="type" column="type" />
            <result property="num" column="num" />
            <result property="texture" column="texture" />
            <result property="changeReason" column="change_reason" />

        </collection>
    </resultMap>
    <select id="getMoneySum" resultType="java.math.BigDecimal">
        select sum(amount) from tender_dtl where  ${ew.sqlSegment}
    </select>
    <select id="tenderPackageVoListbyBillId" resultMap="tenderMap">
        select p.bill_id, p.item_no as pitemNo,p.name as pname ,dtl.* from tender_package p
                                               LEFT JOIN tender_dtl dtl
                                                         on p.dtl_Id=dtl.package_id
        where   ${ew.sqlSegment}
    </select>



</mapper>
