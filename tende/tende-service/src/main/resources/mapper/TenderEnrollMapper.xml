<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.tende.mapper.TenderEnrollMapper">

    <select id="findByCondition" resultType="scrbg.meplat.tende.vo.TenderEnrollVo">
        select te.* ,t.release_date,t.tender_form,t.apply_org_name  from tender_enroll te
        left join  tender t
        on  te.tender_apply_id=t.bill_id
        where  ${ew.sqlSegment}
    </select>
    <select id="Packageslist" resultType="scrbg.meplat.tende.entity.TenderPackage">
        select tp.*  from tender_enroll te
        left join  tender_package tp
        on  te.package_ids=t.package_id

    </select>
</mapper>
