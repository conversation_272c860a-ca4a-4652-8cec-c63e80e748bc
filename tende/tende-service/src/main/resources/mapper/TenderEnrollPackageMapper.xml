<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.tende.mapper.TenderEnrollPackageMapper">

    <select id="Packageslist" resultType="scrbg.meplat.tende.entity.TenderPackage">
        select tp.* from tender_enroll_package tep
                             left join tender_package tp
                                       on tep.package_id=tp.dtl_id
          where   ${ew.sqlSegment}
    </select>
</mapper>
