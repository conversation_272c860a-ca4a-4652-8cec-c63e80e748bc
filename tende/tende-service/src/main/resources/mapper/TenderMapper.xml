<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.tende.mapper.TenderMapper">

    <resultMap id="BaseResultMap" type="scrbg.meplat.tende.entity.Tender">
            <id property="billId" column="bill_id" jdbcType="CHAR"/>
            <id property="releaseDate" column="release_date" jdbcType="TIMESTAMP"/>
            <result property="billNo" column="bill_no" jdbcType="VARCHAR"/>
            <result property="changeReason" column="change_reason" jdbcType="VARCHAR"/>
            <result property="applyOrgId" column="apply_org_id" jdbcType="CHAR"/>
            <result property="applyOrgName" column="apply_org_name" jdbcType="VARCHAR"/>
            <result property="tenderForm" column="tender_form" jdbcType="CHAR"/>
            <result property="tenderName" column="tender_name" jdbcType="VARCHAR"/>
            <result property="tenderNotice" column="tender_notice" jdbcType="VARCHAR"/>
            <result property="state" column="state" jdbcType="INTEGER"/>
            <result property="tenderAmount" column="tender_amount" jdbcType="DECIMAL"/>
            <result property="priceLimit" column="price_limit" jdbcType="DECIMAL"/>
            <result property="orgId" column="org_id" jdbcType="CHAR"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="tenderBail" column="tender_bail" jdbcType="DECIMAL"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="founderName" column="founder_name" jdbcType="VARCHAR"/>
            <result property="founderId" column="founder_id" jdbcType="CHAR"/>
            <result property="lastModifierId" column="last_modifier_id" jdbcType="CHAR"/>
            <result property="lastModifierName" column="last_modifier_name" jdbcType="VARCHAR"/>
            <result property="lastModifyTime" column="last_modify_time" jdbcType="TIMESTAMP"/>
            <result property="workId" column="work_id" jdbcType="CHAR"/>
            <result property="tenderClass" column="tender_class" jdbcType="INTEGER"/>
            <result property="version" column="version" jdbcType="INTEGER"/>
            <result property="baseCurId" column="base_cur_id" jdbcType="CHAR"/>
            <result property="baseCurName" column="base_cur_name" jdbcType="VARCHAR"/>
            <result property="baseCurRate" column="base_cur_rate" jdbcType="DECIMAL"/>
            <result property="rmbRate" column="rmb_rate" jdbcType="DECIMAL"/>
            <result property="baseCurAmount" column="base_cur_amount" jdbcType="DECIMAL"/>
            <result property="tenderEndTime" column="tender_end_time" jdbcType="TIMESTAMP"/>
            <result property="applyTime" column="apply_time" jdbcType="DATE"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="currency" column="currency" jdbcType="VARCHAR"/>
            <result property="currencyId" column="currency_id" jdbcType="CHAR"/>
            <result property="tenderState" column="tender_state" jdbcType="INTEGER"/>
            <result property="nullifyCreated" column="nullify_created" jdbcType="TIMESTAMP"/>
            <result property="nullifyCreator" column="nullify_creator" jdbcType="VARCHAR"/>
            <result property="nullifyCreatorId" column="nullify_creator_id" jdbcType="CHAR"/>
            <result property="nullifyDescription" column="nullify_description" jdbcType="VARCHAR"/>
            <result property="nullifyReason" column="nullify_reason" jdbcType="VARCHAR"/>
            <result property="tenderFee" column="tender_fee" jdbcType="DECIMAL"/>
            <result property="purchaseDemandStatement" column="purchase_demand_statement" jdbcType="VARCHAR"/>
            <result property="tenderUserId" column="tender_user_id" jdbcType="VARCHAR"/>
            <result property="baseCurTenderAmount" column="base_cur_tender_amount" jdbcType="DECIMAL"/>
            <result property="tenderUser" column="tender_user" jdbcType="VARCHAR"/>
            <result property="releaseState" column="release_state" jdbcType="INTEGER"/>
            <result property="leaseType" column="lease_type" jdbcType="INTEGER"/>
            <result property="taxAmount" column="tax_amount" jdbcType="DECIMAL"/>
            <result property="tenderClausePath" column="tender_clause_path" jdbcType="VARCHAR"/>
            <result property="tenderLastState" column="tender_last_state" jdbcType="INTEGER"/>
            <result property="isTransfer" column="is_transfer" jdbcType="INTEGER"/>
            <result property="lastBillId" column="last_bill_id" jdbcType="VARCHAR"/>
            <result property="isMaterial" column="is_material" jdbcType="TINYINT"/>
            <result property="isDevice" column="is_device" jdbcType="TINYINT"/>
            <result property="tenderType" column="tender_type" jdbcType="TINYINT"/>
            <result property="telephone" column="telephone" jdbcType="VARCHAR"/>
    </resultMap>
<!--    <resultMap id="tenderInfoVoMap" type="scrbg.meplat.tende.vo.TenderInfoVo">-->
<!--        <id property="billId" column="bill_id" jdbcType="CHAR"/>-->
<!--        <collection property="tenderPackages" javaType="list" ofType="scrbg.meplat.tende.vo.TenderPackageVo">-->

<!--            -->
<!--        </collection>-->
<!--    </resultMap>-->

    <sql id="Base_Column_List">
        bill_id,release_date,bill_no,
        apply_org_id,apply_org_name,tender_form,
        tender_name,tender_notice,state,
        tender_amount,org_id,tender_adress,
        org_name,tender_bail,remarks,
        founder_name,founder_id,last_modifier_id,
        last_modifier_name,last_modify_time,work_id,
        tender_class,version,base_cur_id,
        base_cur_name,base_cur_rate,rmb_rate,
        base_cur_amount,tender_end_time,apply_time,
        gmt_create,gmt_modified,currency,
        currency_id,tender_state,nullify_created,
        nullify_creator,nullify_creator_id,nullify_description,
        nullify_reason,tender_fee,purchase_demand_statement,
        tender_user_id,base_cur_tender_amount,tender_user,
        release_state,lease_type,tax_amount,
        tender_clause_path,tender_last_state,is_transfer,
        last_bill_id,is_material,is_device,
        tender_type
    </sql>
    <select id="getTenderNum" resultType="scrbg.meplat.tende.vo.TenderResultVo">
        select count(*) AS  biddingProject, sum(tender_amount) as money from tender
                                             where        ${ew.sqlSegment}
    </select>
    <select id="findByCondition" resultType="scrbg.meplat.tende.entity.Tender">
        select distinct te.tender_apply_id ,t.* from tender_enroll te
                 left join  tender t
                  on  te.tender_apply_id=t.bill_id
        where  ${ew.sqlSegment}
    </select>
    <select id="winBidList" resultType="scrbg.meplat.tende.entity.Tender">
        select distinct tps.bill_id as tpsbill ,t.* from tender_package_subcontractor tps
                                                         left join  tender t
                                                                    on  tps.bill_id=t.bill_id
        where  ${ew.sqlSegment}
    </select>
    <select id="getByBillNo" resultType="scrbg.meplat.tende.entity.Tender">
        select * from tender where bill_no=#{billNo}
    </select>
<!--    <select id="findTenderVoById" resultMap="tenderInfoVoMap">-->
<!--        select t.bill_id,tp.dtl_id,tp.bill_id,tp.item_no,tp.name-->
<!--        from  tender t-->
<!--                  left join tender_package tp-->
<!--                            on t.bill_id=tp.bill_id-->
<!--                  left join tender_dtl   td-->
<!--                            on tp.dtl_id=td.package_id-->
<!--where t.bill_id=#{billId}-->

<!--    </select>-->
</mapper>
