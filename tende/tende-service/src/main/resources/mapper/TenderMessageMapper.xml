<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.tende.mapper.TenderMessageMapper">
    <resultMap id="BaseResultMap" type="scrbg.meplat.tende.entity.TenderMessage">
        <id property="messageId" column="imessage_id" />
        <result property="sendId" column="send_id" />
        <result property="sendName" column="send_name" />
        <result property="title" column="title" />
        <result property="messageType" column="message_type" />
        <result property="unitCode" column="unit_code" />
        <result property="isRead" column="is_read" />
        <result property="gmtCreate" column="gmt_create" />
        <result property="readTime" column="read_time" />
        <result property="mallType" column="mall_type" />
        <result property="keyword" column="keyword" />
        <result property="count" column="count" />
        <result property="isDelete" column="is_delete" />
        <result property="gmtModified" column="gmt_modified" />
        <result property="sendTime" column="send_time" />
        <result property="notice" column="notice" />


    </resultMap>
</mapper>
