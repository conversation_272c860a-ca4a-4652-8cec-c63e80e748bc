<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.tende.mapper.TenderNoticeContentMapper">

    <resultMap id="BaseResultMap" type="scrbg.meplat.tende.entity.TenderNoticeContent">
            <id property="recordId" column="record_id" jdbcType="CHAR"/>
            <result property="billId" column="bill_id" jdbcType="CHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        record_id,bill_id,content,
        gmt_create,gmt_modified
    </sql>
</mapper>
