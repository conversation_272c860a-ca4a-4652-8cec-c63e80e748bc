<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.tende.mapper.TenderNoticeMapper">

    <resultMap id="BaseResultMap" type="scrbg.meplat.tende.entity.TenderNotice">
            <id property="billId" column="bill_id" jdbcType="CHAR"/>
            <result property="tenderApplyId" column="tender_apply_id" jdbcType="CHAR"/>
            <result property="tenderApplyNo" column="tender_apply_no" jdbcType="VARCHAR"/>
            <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
            <result property="tenderForm" column="tender_form" jdbcType="VARCHAR"/>
            <result property="isBid" column="is_bid" jdbcType="INTEGER"/>
            <result property="noticeUser" column="notice_user" jdbcType="VARCHAR"/>
            <result property="noticeUserId" column="notice_user_id" jdbcType="CHAR"/>
            <result property="tenderOrgId" column="tender_org_id" jdbcType="CHAR"/>
            <result property="tenderOrgName" column="tender_org_name" jdbcType="VARCHAR"/>
            <result property="state" column="state" jdbcType="INTEGER"/>
            <result property="orgId" column="org_id" jdbcType="CHAR"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="packageId" column="package_id" jdbcType="CHAR"/>
            <result property="packageName" column="package_name" jdbcType="VARCHAR"/>
            <result property="founderId" column="founder_id" jdbcType="CHAR"/>
            <result property="founderName" column="founder_name" jdbcType="VARCHAR"/>
            <result property="releaseState" column="release_state" jdbcType="INTEGER"/>
            <result property="billNo" column="bill_no" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="releaseDate" column="release_date" jdbcType="TIMESTAMP"/>
            <result property="isTransfer" column="is_transfer" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        bill_id,tender_apply_id,tender_apply_no,
        project_name,tender_form,is_bid,
        notice_user,notice_user_id,tender_org_id,
        tender_org_name,state,org_id,
        org_name,gmt_create,gmt_modified,
        package_id,package_name,founder_id,
        founder_name,release_state,bill_no,
        remarks,release_date,is_transfer
    </sql>
</mapper>
