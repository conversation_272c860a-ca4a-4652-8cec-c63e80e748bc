<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.tende.mapper.TenderPackageMapper">

    <resultMap id="BaseResultMap" type="scrbg.meplat.tende.entity.TenderPackage">

           <result property="changeReason" column="change_Reason" />
            <result property="dtlId" column="dtl_id" />
            <result property="billId" column="bill_id" />
            <result property="itemNo" column="item_no" />
            <result property="name" column="name" />
            <result property="priceLimit" column="price_limit" jdbcType="DECIMAL"/>
            <result property="taxRate" column="tax_rate" jdbcType="DECIMAL"/>
            <result property="taxAmount" column="tax_amount" jdbcType="DECIMAL"/>
            <result property="freightRate" column="freight_rate" jdbcType="DECIMAL"/>
            <result property="gmtCreate" column="gmt_create" />
            <result property="gmtModified" column="gmt_modified" />
            <result property="planDetails" column="plan_details" />
            <result property="freightTax" column="freight_tax" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        dtl_id,bill_id,item_no,
        name,price_limit,tax_rate,
        tax_amount,freight_rate,gmt_create,
        gmt_modified,plan_details,freight_tax
    </sql>
    <resultMap id="tenderMap"   type="scrbg.meplat.tende.vo.TenderPackageVo" >
        <id property="dtlId" column="dtl_id" />
        <result property="billId" column="bill_id" />
        <result property="itemNo" column="item_no" />
        <result property="name" column="name" />
    <collection property="list" javaType="list" ofType="scrbg.meplat.tende.entity.TenderPackageSubcontractor">
        <id property="recordId" column="record_id" />
        <result property="packageId" column="package_id" />
        <result property="billId" column="bill_id" />
        <result property="subcontractorName" column="subcontractor_name" />
        <result property="subcontractorId" column="subcontractor_id" />
        <result property="gmtCreate" column="gmt_create" />
        <result property="gmtModified" column="gmt_modified" />
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="creditCode" column="credit_code" />
        <result property="relationUser" column="relation_user" />
        <result property="relationPhone" column="relation_phone" />
    </collection>
    </resultMap>
    <resultMap id="tenderDtlMap"   type="scrbg.meplat.tende.vo.TenderPackageVo">
        <id property="dtlId" column="tp.dtl_id" />
        <result property="billId" column="tp.bill_id" />
        <result property="itemNo" column="tp.item_no" />
        <result property="name" column="tp.name" />
        <collection property="tenderDtlList" javaType="list" ofType="scrbg.meplat.tende.entity.TenderDtl">
            <id property="recordId" column="record_id" />
            <result property="packageId" column="package_id" />
            <result property="billId" column="bill_id" />
            <result property="type" column="type" />
            <result property="name" column="name" />
            <result property="specs" column="specs" />
            <result property="unit" column="unit" />
            <result property="type" column="type" />
            <result property="texture" column="texture" />
            <result property="limitPrice" column="limit_price" />
            <result property="num" column="num" />

        </collection>
    </resultMap>
    <select id="tenderPackageVoListbyBillId" resultMap="tenderMap">
        select p.item_no,p.name ,ps.* from tender_package p
                       LEFT JOIN tender_package_subcontractor ps
                        on p.dtl_Id=ps.package_id
        where
        ${ew.sqlSegment}
    </select>
    <select id="winBidPackageList" resultType="scrbg.meplat.tende.entity.TenderPackage">
        select distinct tn.package_id as tpsbill ,tp.* from tender_notice tn
                                                             left join  tender_package tp
                                                                        on  tn.package_id=tp.dtl_id
        where  ${ew.sqlSegment}
    </select>
    <select id="tenderDtlListbyBillId" resultMap="tenderDtlMap">
        select tp.dtl_id,tp.bill_id,tp.item_no,tp.name,td.* from tender_package tp
             left join tender_dtl   td
             on tp.dtl_id=td.package_id
        where
            ${ew.sqlSegment}
    </select>
</mapper>
