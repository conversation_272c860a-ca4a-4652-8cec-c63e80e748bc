<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.tende.mapper.TenderPackageSubcontractorMapper">

    <resultMap id="BaseResultMap" type="scrbg.meplat.tende.entity.TenderPackageSubcontractor">
            <id property="recordId" column="record_id" jdbcType="CHAR"/>
           <result property="changeReason" column="change_Reason" jdbcType="VARCHAR"/>
            <result property="packageId" column="package_id" jdbcType="CHAR"/>
            <result property="billId" column="bill_id" jdbcType="VARCHAR"/>
            <result property="subcontractorName" column="subcontractor_name" jdbcType="VARCHAR"/>
            <result property="subcontractorId" column="subcontractor_id" jdbcType="CHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="creditCode" column="credit_code" jdbcType="VARCHAR"/>
            <result property="relationUser" column="relation_user" jdbcType="VARCHAR"/>
            <result property="relationPhone" column="relation_phone" jdbcType="VARCHAR"/>


    </resultMap>

    <sql id="Base_Column_List">
        record_id,package_id,bill_id,
        subcontractor_name,subcontractor_id,gmt_create,
        gmt_modified,type,credit_code
    </sql>
</mapper>
