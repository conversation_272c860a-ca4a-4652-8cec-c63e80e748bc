package ${package.Entity};

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @描述：$!{table.comment}
 * @作者: ${author}
 * @日期: ${date}
 */
@ApiModel(value="$!{table.comment}")
@Data
@TableName("${table.name}")
public class ${entity} implements Serializable {

private static final long serialVersionUID = 1L;

## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    #if(${field.keyFlag})
        #set($keyPropertyName=${field.propertyName})
    #end
    #if($!field.propertyName.indexOf('Id') != -1 && ${foreach.index} == '0')
    @TableId(type = IdType.ASSIGN_ID)
    #end
    #if($!field.propertyName.endsWith('Date'))
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    #end
    #if("$!field.comment" != "")
        #if(${field.propertyName}=="createTime"||${field.propertyName}=="currencyId"||${field.propertyName}=="currency"
        ||${field.propertyName}=="scDicValue"||${field.propertyName}=="scDicValueName"
        ||${field.propertyName}=="rmbRate"||${field.propertyName}=="scRate"
        ||$!field.propertyName.startsWith('rmb') || $!field.propertyName.startsWith('sca')
        ||$!field.propertyName.startsWith('last')
        )
    @JsonIgnore
    @ApiModelProperty(value = "${field.comment}", hidden = true)
        #else
    @ApiModelProperty(value = "${field.comment}")
        #end
    #end

    #if(${field.propertyType}=="Blob")
        private byte[] ${field.propertyName};
    #else
        private ${field.propertyType} ${field.propertyName};
    #end
##    private ${field.propertyType} ${field.propertyName}#if(${field.propertyType} == "BigDecimal") = new BigDecimal(0)#end;

#end

}