package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;

/**
 * @描述：$!{table.comment} 服务类
 * @作者: ${author}
 * @日期: ${date}
 */
@Service
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName}{
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper queryWrapper) {
        IPage<${entity}> page = this.page(
        new Query<${entity}>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(${entity} ${table.entityPath}) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(${table.entityPath});
    }

    @Override
    public void update(${entity} ${table.entityPath}) {
        super.updateById(${table.entityPath});
    }


    @Override
    public ${entity} getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

}