package scrbg.meplat.tende;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import scrbg.meplat.tende.entity.Tender;
import scrbg.meplat.tende.service.TenderService;
import scrbg.meplat.tende.util.DateUtil;

import java.io.FileInputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@SpringBootTest
public class DateUtils {
    @Autowired
    private TenderService tenderService;


    @Test
    public void comparisonDate() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        String dateString = "2023-07-30 00:00:00";
        Date parse = null;
        try {
            parse = df.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        boolean after = parse.before(new Date());
        System.out.println(after);


    }
    @Test
    public void classMasdfange2() {
        List<Tender> list = tenderService.list();
        for (Tender tender : list) {
            if (tender.getReleaseState()==1){
                if (tender.getTenderState()==2){
                    tender.setTenderState(3);

                }
                tender.setIsShow(1);
            }else {
                tender.setTenderState(2);
            }
        }
        tenderService.updateBatchById(list);


    }
    @Test
    public void classMasdfange1() {
        try {
            HSSFWorkbook xssfWorkbook = new HSSFWorkbook(new FileInputStream("D://workfiles//1.xls"));
            HSSFSheet hssfWorkbook = xssfWorkbook.getSheet("Sheet1");
            //通过sheet的名字来获取数据

            //通过下标来获取数据
            int firstRowNum = hssfWorkbook.getFirstRowNum();
            //获取第一行的下标
            int lastRowNum = hssfWorkbook.getLastRowNum();
            List<Tender> list = new ArrayList<>();
            for (int i = firstRowNum; i <= lastRowNum; i++) {
                Row row = hssfWorkbook.getRow(i);
                //根据下标，获取对应行的数据
                int firstCellNum = row.getFirstCellNum();
                //获取对应行的第一个cell的下标
                int lastCellNum = row.getLastCellNum();
                //获取对应行的最后一个cell的下标
                /**
                 * 注意：比如这一行有四个单元格，则firstCellNum=0，lastCellNum=4，注意=4！！！！！，并不是等于3
                 * 所以下面的for循环为<
                 * 之所以没有在循环外就确定这两个值，是因为你没有办法确定每一行的列数都一致
                 * */
                Tender tender = new Tender();
                //新建一个list用来存放数据
                tender.setBillId(String.valueOf(row.getCell(0)));
                tender.setBillNo(String.valueOf(row.getCell(1)));
                SimpleDateFormat df = new SimpleDateFormat("yyyy/MM/dd hh:mm:ss");
                Date parse = null;
                try {
                    parse = df.parse(String.valueOf(row.getCell(2)));

                } catch (ParseException e) {
                    e.printStackTrace();
                }

                tender.setReleaseDate(parse);
                String string = row.getCell(3).toString();
                int dotIndex = string.indexOf(".");

                tender.setTenderState(dotIndex);

//                for (int j=firstCellNum;j<lastCellNum;j++){
//                    Cell cell=row.getCell(j);
//                    //通过下标获取对应的单元格的信息
//                    System.out.print(cell+"\t");
//
//
//                }
                list.add(tender);
                System.out.println();
                //确保每一行数据打印出来都跨行
            }
            List<Tender> updateList = new ArrayList<>();
            for (Tender tender : list) {
                Tender one = tenderService.lambdaQuery().eq(Tender::getBillId, tender.getBillId()).eq(Tender::getBillNo, tender.getBillNo()).one();
                if (one!=null){
                    one.setReleaseDate(tender.getReleaseDate());
                    one.setTenderState(tender.getTenderState());
                    one.setReleaseState(1);
                    updateList.add(one);
                }
            }
            tenderService.updateBatchById(updateList);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }
}
