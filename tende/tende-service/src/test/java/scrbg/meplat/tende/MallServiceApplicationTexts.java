package scrbg.meplat.tende;

import com.alibaba.fastjson.JSONObject;
import com.scrbg.common.utils.R;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.tende.config.HttpConfig;
import scrbg.meplat.tende.util.RestTemplateUtils;

import java.io.*;
import java.util.*;

@Slf4j
@SpringBootTest
class MallServiceApplicationTexts {

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    HttpConfig httpConfig;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private RestTemplateUtils restTemplateUtils;

    @Test
    public void text2() {
            String filePath = "D:\\新建文件夹\\json.txt";
        FileInputStream fin = null;
        try {
            fin = new FileInputStream(filePath);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        InputStreamReader reader = new InputStreamReader(fin);
            BufferedReader buffReader = new BufferedReader(reader);
            String strTmp = "";
        StringBuffer stringBuffer = new StringBuffer();
            while(true){
                try {
                    if (!((strTmp = buffReader.readLine())!=null)) break;
                } catch (IOException e) {
                    e.printStackTrace();
                }

                stringBuffer.append(strTmp);

            }
        String s = stringBuffer.toString();
        rabbitTemplate.convertAndSend("mall-tender2",s);
        try {
            buffReader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }







    @Test
    public void text222() {
        String socialCrede="91510107MA6C89AF3B";
        Integer mallType =0;
        String a="http://172.17.1.216:9010/w/user/getIsSupplier?socialCreditCode="+socialCrede+",mallType"+mallType;
//        HttpClientUtil.doGet();



    }
    @Test
    public void text1() {
        String a="{\"method\":\"create\",\"data\":{\"tenderForm\":\"4\",\"tenderState\":2,\"tenderUser\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"tenderNotice\":\"\",\"tenderPackages\":[{\"dtlId\":\"1625306212281094145\",\"gmtModified\":\"2023-02-14 09:29:32\",\"itemNo\":\"KM1314\",\"priceLimit\":81252.0000,\"gmtCreate\":\"2023-02-14 09:29:32\",\"freightRate\":1.2000,\"taxRate\":5.50,\"planDetails\":\"[{\\\"billid\\\":\\\"1624950158944563202\\\",\\\"billNo\\\":\\\"SFGSTZGSLTJ6JCHZ20230213001\\\",\\\"type\\\":7}]\",\"tenderDtls\":[{\"gmtModified\":\"2023-02-14 09:29:32\",\"materialTypeId\":\"a927249575ee-ba4a-3d4e-bcef-a93d5c45/a997201ed117-a448-694e-3405-806062d4/a9972033ea25-9428-7244-9460-05336df0\",\"num\":222.0000,\"type\":\"周转材料/其他类/汽车衡\",\"recordId\":\"1625306212335620097\",\"specs\":\"\",\"freightPrice\":11.0000,\"unitPrice\":122.0000,\"amount\":27084.0000,\"limitPrice\":111.0000,\"texture\":\"铁\",\"dataType\":7,\"materialNameId\":\"1624950158944563202\",\"packageId\":\"1625306212281094145\",\"gmtCreate\":\"2023-02-14 09:29:32\",\"materialId\":\"a99720926472-9ca2-bb41-b818-180c7952\",\"unit\":\"台\",\"billId\":\"1625306212218179585\",\"name\":\"汽车衡\"},{\"gmtModified\":\"2023-02-14 09:29:34\",\"materialTypeId\":\"a927249575ee-ba4a-3d4e-bcef-a93d5c45/a997201ed117-a448-694e-3405-806062d4/a997203531a5-bdf1-264e-b86f-b5d28a12\",\"num\":222.0000,\"type\":\"周转材料/其他类/电机、泵类\",\"recordId\":\"1625306219138781186\",\"specs\":\"\",\"freightPrice\":11.0000,\"unitPrice\":122.0000,\"amount\":27084.0000,\"limitPrice\":111.0000,\"texture\":\"铁\",\"dataType\":7,\"materialNameId\":\"1624950158944563202\",\"packageId\":\"1625306212281094145\",\"gmtCreate\":\"2023-02-14 09:29:34\",\"materialId\":\"aa44170fe785-bc09-5f4f-c3ae-b8c173e2\",\"unit\":\"台\",\"billId\":\"1625306212218179585\",\"name\":\"泥浆处理器\"},{\"gmtModified\":\"2023-02-14 09:29:34\",\"materialTypeId\":\"a927249575ee-ba4a-3d4e-bcef-a93d5c45/a997201ed117-a448-694e-3405-806062d4/a997203531a5-bdf1-264e-b86f-b5d28a12\",\"num\":222.0000,\"type\":\"周转材料/其他类/电机、泵类\",\"recordId\":\"1625306220959109122\",\"specs\":\"\",\"freightPrice\":11.0000,\"unitPrice\":122.0000,\"amount\":27084.0000,\"limitPrice\":111.0000,\"texture\":\"铁\",\"dataType\":7,\"materialNameId\":\"1624950158944563202\",\"packageId\":\"1625306212281094145\",\"gmtCreate\":\"2023-02-14 09:29:34\",\"materialId\":\"aa46207ef997-86b6-5745-ab09-e7709fb1\",\"unit\":\"套\",\"billId\":\"1625306212218179585\",\"name\":\"螺旋输送机\"}],\"billId\":\"1625306212218179585\",\"name\":\"包间1\",\"freightTax\":87.9100,\"taxAmount\":4065.9300},{\"dtlId\":\"1625306291758960641\",\"gmtModified\":\"2023-02-14 09:32:13\",\"itemNo\":\"wa123123\",\"priceLimit\":10000,\"freightRate\":1.1,\"taxRate\":2.2,\"planDetails\":\"[{\\\"billid\\\":\\\"1623499035451068417\\\",\\\"billNo\\\":\\\"SFGSTZGSLTJ6CGJH20230209001\\\",\\\"type\\\":3}]\",\"tenderDtls\":[{\"materialTypeId\":\"a927249575ee-ba4a-3d4e-bcef-a93d5c45/f133dc10-c55b-4323-a999-6a12c2c6e848/1b0bbb50-809a-47ae-a885-edffd0ea8b61\",\"num\":50,\"type\":\"周转材料/模板类/挂篮\",\"recordId\":\"1625306588967342082\",\"specs\":\"\",\"freightPrice\":50,\"unitPrice\":100,\"amount\":5000,\"limitPrice\":50,\"texture\":\"胶\",\"dataType\":3,\"materialNameId\":\"a997214ff50a-8ada-ee47-dac3-5a6f28f5\",\"packageId\":\"1625306291758960641\",\"materialId\":\"1625306537838776322\",\"unit\":\"t\",\"billId\":\"1625306212218179585\",\"name\":\"挂篮\"},{\"materialTypeId\":\"a927249575ee-ba4a-3d4e-bcef-a93d5c45/f133dc10-c55b-4323-a999-6a12c2c6e848/1b0bbb50-809a-47ae-a885-edffd0ea8b61\",\"num\":50,\"type\":\"周转材料/模板类/挂篮\",\"recordId\":\"1625306589571321858\",\"specs\":\"\",\"freightPrice\":50,\"unitPrice\":100,\"amount\":5000,\"limitPrice\":50,\"texture\":\"纸\",\"dataType\":3,\"materialNameId\":\"aa482300cd28-afda-3743-1000-8c3fb4c6\",\"packageId\":\"1625306291758960641\",\"materialId\":\"1625306537838776323\",\"unit\":\"套\",\"billId\":\"1625306212218179585\",\"name\":\"其他附件\"}],\"billId\":\"1625306212218179585\",\"name\":\"包件2\",\"freightTax\":55,\"taxAmount\":110}],\"baseCurName\":\"人民币\",\"changeDate\":\"2023-02-14 00:00:00\",\"baseCurId\":\"0\",\"state\":2,\"applyTime\":\"2023-02-13 00:00:00\",\"billNo\":\"SFGSTZGSLTJ6ZBWZGY20230213001BY01\",\"tenderBail\":20000,\"applyOrgId\":\"1572997388618956809\",\"orgName\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"version\":0,\"founderId\":\"aa06244aa0e8-a57b-4f4f-61ce-548d2666\",\"tenderAmount\":81252,\"billId\":\"1625306212218179585\",\"taxAmount\":4153.84,\"tenderClass\":3,\"tenderFee\":20000,\"baseCurAmount\":0,\"gmtModified\":\"2023-02-14 09:29:32\",\"tenderName\":\"pd2/13物资供应招标\",\"tenderUserId\":\"1572997388618956809\",\"orgId\":\"1572997388618956809\",\"tenderEndTime\":\"2023-02-28 00:00:00\",\"baseCurTenderAmount\":0,\"changeReason\":\"111\",\"baseCurRate\":0,\"releaseDate\":\"2023-02-14\",\"billType\":\"60231\",\"founderName\":\"樊景盛\",\"gmtCreate\":\"2023-02-14 09:29:32\",\"workId\":\"1625306892119052289\",\"applyOrgName\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"releaseState\":1,\"purchaseDemandStatement\":\"111\",\"remarks\":\"\"},\"tableName\":\"Tender\"}";
        rabbitTemplate.convertAndSend("mall-chang",a);


    }

    @Test
    public void tender() {
String a="{\n" +
        "\t\"method\": \"create\",\n" +
        "\t\"data\": {\n" +
        "\t\t\"applyOrgId\": \"a94220dc350d-8522-4b4e-9be0-bef15885\",\n" +
        "\t\t\"applyOrgName\": \"四川公路桥梁建设集团有限公司绵西高速公路TJA标段项目经理部\",\n" +
        "\t\t\"applyTime\": \"2023-05-05 16:10:21\",\n" +
        "\t\t\"baseCurAmount\": 2250000.0000,\n" +
        "\t\t\"baseCurId\": \"0\",\n" +
        "\t\t\"baseCurName\": \"人民币\",\n" +
        "\t\t\"baseCurRate\": 1.0000,\n" +
        "\t\t\"baseCurTenderAmount\": 2250000.0000,\n" +
        "\t\t\"billId\": \"aff922bc7c20-b065-5c42-28ea-c018727d\",\n" +
        "\t\t\"billNo\": \"SRBGMXTJA000ZBWZGY853\",\n" +
        "\t\t\"billType\": \"1\",\n" +
        "\t\t\"changeDate\": null,\n" +
        "\t\t\"changeReason\": null,\n" +
        "\t\t\"founderId\": \"aac614b499ff-a749-fc42-72b7-dc4fce3a\",\n" +
        "\t\t\"founderName\": \"aac614b499ff-a749-fc42-72b7-dc4fce3a\",\n" +
        "\t\t\"gmtCreate\": \"2023-05-05 16:11:17\",\n" +
        "\t\t\"gmtModified\": \"2023-05-05 16:10:21\",\n" +
        "\t\t\"isDevice\": 0,\n" +
        "\t\t\"isMaterial\": 1,\n" +
        "\t\t\"isTransfer\": 0,\n" +
        "\t\t\"leaseType\": 0,\n" +
        "\t\t\"nullifyCreated\": null,\n" +
        "\t\t\"nullifyCreator\": null,\n" +
        "\t\t\"nullifyCreatorId\": null,\n" +
        "\t\t\"nullifyDescription\": null,\n" +
        "\t\t\"nullifyReason\": null,\n" +
        "\t\t\"num\": 0.0,\n" +
        "\t\t\"orgId\": \"a94220dc350d-8522-4b4e-9be0-bef15885\",\n" +
        "\t\t\"orgName\": \"四川公路桥梁建设集团有限公司绵西高速公路TJA标段项目经理部\",\n" +
        "\t\t\"priceLimit\": 0.0,\n" +
        "\t\t\"purchaseDemandStatement\": \"111\",\n" +
        "\t\t\"releaseDate\": null,\n" +
        "\t\t\"releaseState\": 0,\n" +
        "\t\t\"remarks\": \"\",\n" +
        "\t\t\"state\": 2,\n" +
        "\t\t\"taxAmount\": 0.0000,\n" +
        "\t\t\"telephone\": \"\",\n" +
        "\t\t\"tenderAmount\": 2250000.0000,\n" +
        "\t\t\"tenderBail\": 0.0,\n" +
        "\t\t\"tenderClass\": 1,\n" +
        "\t\t\"tenderClausePath\": \"\",\n" +
        "\t\t\"tenderEndTime\": \"2023-05-06 16:10:20\",\n" +
        "\t\t\"tenderFee\": 250.0,\n" +
        "\t\t\"tenderForm\": \"1\",\n" +
        "\t\t\"tenderLastState\": 0,\n" +
        "\t\t\"tenderName\": \"杨程用物资采购招标\",\n" +
        "\t\t\"tenderNotice\": \"<HTML><BODY><DIV /></BODY></HTML>\",\n" +
        "\t\t\"tenderPackages\": [{\n" +
        "\t\t\t\"billId\": \"aff922bc7c20-b065-5c42-28ea-c018727d\",\n" +
        "\t\t\t\"deliveryAdress\": \"\",\n" +
        "\t\t\t\"deliveryDate\": \"\",\n" +
        "\t\t\t\"dtlId\": \"c6ee964b-1d9d-4cd8-8d82-c891aa787e07\",\n" +
        "\t\t\t\"freightRate\": 0.0,\n" +
        "\t\t\t\"freightTax\": 0,\n" +
        "\t\t\t\"gmtCreate\": \"2023-05-05 04:10:21\",\n" +
        "\t\t\t\"gmtModified\": \"2023-05-05 04:10:21\",\n" +
        "\t\t\t\"itemNo\": \"1\",\n" +
        "\t\t\t\"materials\": \"\",\n" +
        "\t\t\t\"materialsSku\": \"\",\n" +
        "\t\t\t\"name\": \"123\",\n" +
        "\t\t\t\"planDetails\": \"\",\n" +
        "\t\t\t\"priceLimit\": 2250000.0000,\n" +
        "\t\t\t\"quantityUnit\": \"\",\n" +
        "\t\t\t\"taxAmount\": 0.0,\n" +
        "\t\t\t\"taxRate\": 0.0,\n" +
        "\t\t\t\"tenderDtls\": [{\n" +
        "\t\t\t\t\"amount\": 2250000.0000,\n" +
        "\t\t\t\t\"baseLibraryId\": \"a1f93f53-ae2a-485d-a27f-0630b025f12e\",\n" +
        "\t\t\t\t\"billId\": \"aff922bc7c20-b065-5c42-28ea-c018727d\",\n" +
        "\t\t\t\t\"dataType\": 1,\n" +
        "\t\t\t\t\"disposalAmount\": 0,\n" +
        "\t\t\t\t\"estimatedEndTime\": \"2023-05-06 04:10:20\",\n" +
        "\t\t\t\t\"estimatedServiceTime\": 0,\n" +
        "\t\t\t\t\"estimatedStartTime\": \"\",\n" +
        "\t\t\t\t\"freightPrice\": 0.0000,\n" +
        "\t\t\t\t\"freightTax\": 0.0,\n" +
        "\t\t\t\t\"gmtCreate\": \"2023-05-05 04:11:17\",\n" +
        "\t\t\t\t\"gmtModified\": \"2023-05-05 04:10:21\",\n" +
        "\t\t\t\t\"limitPrice\": 15000.0000,\n" +
        "\t\t\t\t\"materialId\": \"a1f93f53-ae2a-485d-a27f-0630b025f12e\",\n" +
        "\t\t\t\t\"materialNameId\": \"\",\n" +
        "\t\t\t\t\"materialTypeId\": \"\",\n" +
        "\t\t\t\t\"name\": \"花纹钢板\",\n" +
        "\t\t\t\t\"num\": 150.0000,\n" +
        "\t\t\t\t\"packageId\": \"c6ee964b-1d9d-4cd8-8d82-c891aa787e07\",\n" +
        "\t\t\t\t\"recordId\": \"93f51cb0-156d-4405-a346-c0cfddbc907c\",\n" +
        "\t\t\t\t\"recordType\": 2,\n" +
        "\t\t\t\t\"scrapApplicationNo\": \"\",\n" +
        "\t\t\t\t\"sisposalMethod\": \"\",\n" +
        "\t\t\t\t\"specs\": \"2.5mm以下\",\n" +
        "\t\t\t\t\"texture\": null,\n" +
        "\t\t\t\t\"timingUnit\": \"\",\n" +
        "\t\t\t\t\"type\": \"\",\n" +
        "\t\t\t\t\"unit\": \"t\",\n" +
        "\t\t\t\t\"unitPrice\": 15000\n" +
        "\t\t\t}],\n" +
        "\t\t\t\"tenderPackageSubcontractors\": []\n" +
        "\t\t}],\n" +
        "\t\t\"tenderState\": 2,\n" +
        "\t\t\"tenderType\": 4,\n" +
        "\t\t\"tenderUser\": \"\",\n" +
        "\t\t\"tenderUserId\": \"\",\n" +
        "\t\t\"version\": 0,\n" +
        "\t\t\"workId\": \"aff922bca8fe-9e4c-cc43-bfb4-52f3ea97\"\n" +
        "\t},\n" +
        "\t\"tableName\": \"TenderInfoVo\"\n" +
        "}";
rabbitTemplate.convertAndSend("mall-tender",a);


    }
    @Test
    public void text3() {
        String a="{\"method\":\"create\",\"data\":{\"tenderForm\":\"4\",\"evaluationRecord\":\"111\",\"orgId\":\"1572997388618956809\",\"tenderResultCandidates\":[{\"gmtModified\":\"2023-02-14 09:44:51\",\"twoAmount\":0.0000,\"packageId\":\"1624950520626024449\",\"threeCandidate\":\"成都永吉鑫钢结构工程有限公司\",\"firstCandidateId\":\"a9931501dd7a-94b9-3b44-a26a-dc573e5d\",\"isFail\":0,\"gmtCreate\":\"2023-02-14 09:44:51\",\"firstCandidate\":\"成都中顺桥梁机械有限公司\",\"threeAmount\":0.0000,\"packageNo\":\"KM1314\",\"firstGrade\":0.0000,\"recordId\":\"1625310063860125697\",\"subordinateReferenceOrgName\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"subordinateReferenceOrgId\":\"1572997388618956809\",\"threeCandidateId\":\"a99315014fc4-8209-e04e-57bb-32c97561\",\"billId\":\"1625310063805599745\",\"firstAmount\":0.0000,\"packageName\":\"包间1\",\"twoGrade\":0.0000,\"threeGrade\":0.0000},{\"gmtModified\":\"2023-02-14 09:44:51\",\"twoAmount\":0.0000,\"packageId\":\"1625306291758960641\",\"threeCandidate\":\"\",\"twoCandidateId\":\"\",\"firstCandidateId\":\"\",\"isFail\":1,\"gmtCreate\":\"2023-02-14 09:44:51\",\"firstCandidate\":\"\",\"threeAmount\":0.0000,\"packageNo\":\"wa123123\",\"firstGrade\":0.0000,\"recordId\":\"1625310063864320001\",\"subordinateReferenceOrgName\":\"\",\"subordinateReferenceOrgId\":\"\",\"threeCandidateId\":\"\",\"billId\":\"1625310063805599745\",\"firstAmount\":0.0000,\"packageName\":\"包件2\",\"twoCandidate\":\"\",\"twoGrade\":0.0000,\"threeGrade\":0.0000}],\"tenderApplyOrgId\":\"1572997388618956809\",\"tenderApplyId\":\"1624950176097505281\",\"state\":2,\"billNo\":\"PBJGDJ43\",\"orgName\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"isTransfer\":0,\"billType\":\"60235\",\"founderName\":\"樊景盛\",\"gmtCreate\":\"2023-02-14 09:44:51\",\"tenderApplyOrgName\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"workId\":\"1625310673858727938\",\"founderId\":\"aa06244aa0e8-a57b-4f4f-61ce-548d2666\",\"tenderAmount\":81252.0000,\"billId\":\"1625310063805599745\",\"tenderApplyNo\":\"SFGSTZGSLTJ6ZBWZGY20230213001\",\"projectName\":\"pd2/13物资供应招标\",\"tenderClass\":3,\"remarks\":\"\",\"reportTime\":\"2023-02-14 00:00:00\"},\"tableName\":\"TenderResult\"}";
        rabbitTemplate.convertAndSend("mall-result",a);


    }
    @Test
    public void text4() {
        String a="{\"method\":\"create\",\"data\":{\"tenderForm\":\"4\",\"gmtModified\":\"2023-02-14 09:51:55\",\"orgId\":\"1572997388618956809\",\"tenderOrgName\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"tenderOrgId\":\"1572997388618956809\",\"tenderApplyId\":\"1624950176097505281\",\"startTime\":\"2023-02-14 00:00:00\",\"state\":10,\"billNo\":\"JGGS16\",\"tenderPublicityCandidates\":[{\"gmtModified\":\"2023-02-14 09:51:56\",\"packageId\":\"1624950520626024449\",\"threeCandidate\":\"成都永吉鑫钢结构工程有限公司\",\"firstCandidateId\":\"a9931501dd7a-94b9-3b44-a26a-dc573e5d\",\"gmtCreate\":\"2023-02-14 09:51:56\",\"firstCandidate\":\"成都中顺桥梁机械有限公司\",\"packageNo\":\"KM1314\",\"recordId\":\"1625311845860511746\",\"threeCandidateId\":\"a99315014fc4-8209-e04e-57bb-32c97561\",\"billId\":\"1625311843293597698\",\"packageName\":\"包间1\"},{\"gmtModified\":\"2023-02-14 09:51:56\",\"packageId\":\"1625306291758960641\",\"threeCandidate\":\"\",\"twoCandidateId\":\"\",\"firstCandidateId\":\"\",\"gmtCreate\":\"2023-02-14 09:51:56\",\"firstCandidate\":\"\",\"packageNo\":\"wa123123\",\"recordId\":\"1625311849245315074\",\"threeCandidateId\":\"\",\"billId\":\"1625311843293597698\",\"packageName\":\"包件2\",\"twoCandidate\":\"\"}],\"orgName\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"isTransfer\":0,\"releaseDate\":\"2023-02-14 00:00:00\",\"billType\":\"60234\",\"founderName\":\"樊景盛\",\"gmtCreate\":\"2023-02-14 09:51:55\",\"founderId\":\"aa06244aa0e8-a57b-4f4f-61ce-548d2666\",\"billId\":\"1625311843293597698\",\"endTime\":\"2023-02-28 00:00:00\",\"releaseState\":1,\"tenderApplyNo\":\"SFGSTZGSLTJ6ZBWZGY20230213001\",\"projectName\":\"pd2/13物资供应招标\",\"tenderClass\":3,\"ntryDate\":\"2023-02-14 00:00:00\",\"remarks\":\"\"},\"tableName\":\"TenderPublicity\"}";
        rabbitTemplate.convertAndSend("mall-publicity",a);


    }



    @Test
    public void tenderEnroll() {
        String a="{\"method\":\"create\",\"data\":{\"zipCode\":\"\",\"gmtModified\":1679846400000,\"nullifyReason\":\"\",\"isAbroad\":0,\"nullifyDescription\":\"\",\"orgId\":\"1572997388618956809\",\"tenderType\":3,\"legalRepresentative\":\"王五\",\"taxpayerType\":\"一般纳税人\",\"tenderApplyId\":\"1634091876900409345\",\"unitCode\":\"913701050548576409\",\"unitId\":\"1640195552182276098\",\"applyUnit\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"applyUnitId\":\"\",\"packageName\":\"zxl\",\"state\":2,\"fax\":\"\",\"fixedTelephone\":\"\",\"email\":\"\",\"outsourceType\":\"\",\"nullifyCreator\":\"\",\"orgName\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"unitName\":\"四川公路桥梁建设集团有限公司镇广高速王坪至通江段A2合同段项目经理部\",\"isTransfer\":0,\"billType\":\"60233\",\"telephone\":\"13313311133\",\"gmtCreate\":1679846400000,\"packageIds\":\"\",\"workId\":\"1640195629604954114\",\"nullifyCreatorId\":\"\",\"registerAddress\":\"{\\\"code\\\":[\\\"100000\\\",\\\"410000\\\",\\\"410300\\\",\\\"410323\\\"],\\\"address\\\":[\\\"中华人民共和国\\\",\\\"河南省\\\",\\\"洛阳市\\\",\\\"新安县\\\"],\\\"detail\\\":\\\"\\\"}\",\"enrollTime\":1679846400000,\"registerCaptial\":18000.0000,\"billId\":\"1640195552194859009\",\"proxyUser\":\"\",\"enrollPackages\":[{\"recordId\":\"1640195560415694850\",\"billId\":\"1640195552194859009\",\"itemNo\":\"20230310\",\"priceLimit\":39600.0000,\"gmtCreate\":1679888070000}],\"tenderApplyNo\":\"SFGSTZGSLTJ6ZBWZGY20230310002\",\"projectName\":\"公开招标测试\"},\"tableName\":\"TenderEnroll\"}";
        rabbitTemplate.convertAndSend("tender-enroll",a);


    }
    @Test
    public void tenderNotis() {
        String a="{\"method\":\"create\",\"data\":{\"tenderForm\":\"1\",\"gmtModified\":1679904490000,\"content\":\"<p>中标</p>\",\"orgId\":\"1572997388618956809\",\"tenderOrgName\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"isBid\":1,\"tenderOrgId\":\"1572997388618956809\",\"tenderApplyId\":\"1627961007894695938\",\"noticeUser\":\"四川路桥建设集团股份有限公司\",\"packageName\":\"名称6\",\"state\":0,\"billNo\":\"TZS24\",\"orgName\":\"四川公路桥梁建设集团有限公司铜资高速公路TJ6项目经理部\",\"isTransfer\":0,\"releaseDate\":1679846400000,\"billType\":\"60236\",\"packageId\":\"1627961075662065665\",\"founderName\":\"樊景盛\",\"gmtCreate\":1679904490000,\"founderId\":\"aa06244aa0e8-a57b-4f4f-61ce-548d2666\",\"noticeUserId\":\"1640261598620684290\",\"billId\":\"1640264432032419842\",\"releaseState\":1,\"tenderApplyNo\":\"SFGSTZGSLTJ6ZBWZGY20230221002\",\"projectName\":\"沈一万2.21物资供应招标02\",\"remarks\":\"\"},\"tableName\":\"TenderNotice\"}";
        rabbitTemplate.convertAndSend("tender-notice",a);


    }


    public   void  getHttp() {
        String url2 ="" + "/outer/supplier/find/by/supplier/creditCode?creditCode="
                + "";
        HttpHeaders headers2 = new HttpHeaders();
        headers2.add("token", "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA");
        R isSupp = restTemplateUtils.get(url2, headers2, R.class).getBody();
        System.out.println(isSupp);
    }


    @Test
    public  void ttt(){
        String socialCrede="91511181MA6282FP9P";
        Integer mallType =0;
        String a="http://172.17.1.216:9011/w/user/getIsSupplier?socialCreditCode="+socialCrede+"&&mallType="+mallType;
        String forObject = restTemplate.getForObject(a, String.class);
        System.out.println(forObject);
    }


    @Test void  aa(){
        String path = "D:\\新建文件夹\\a.txt";
        String word = "试试3435";
        BufferedWriter out = null;
        try {
            out = new BufferedWriter(
                    new OutputStreamWriter(new FileOutputStream(path,true)));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        try {
            out.write(word+"\r\n");
        } catch (IOException e) {
            e.printStackTrace();
        }
        try {
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }



}
